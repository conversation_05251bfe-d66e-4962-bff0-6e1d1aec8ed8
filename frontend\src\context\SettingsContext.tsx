import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { settingsService, SiteSettings } from '@/services/settingsService';
 
interface SettingsContextType {
  siteSettings: SiteSettings | null;
  isLoading: boolean;
  updateSiteSettings: (formData: FormData) => Promise<void>;
  updateAdminProfile: (formData: FormData) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settings = await settingsService.getSiteSettings();
        setSiteSettings(settings);
      } catch (error) {
        console.error('Error fetching site settings:', error);
        toast({
          title: "Error",
          description: "Failed to load site settings",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    // fetchSettings();
  }, [toast]);

  const updateSiteSettings = async (formData: FormData) => {
    setIsLoading(true);
    try {
      const updatedSettings = await settingsService.updateSiteSettings(formData);
      setSiteSettings(updatedSettings);
      toast({
        title: "Success",
        description: "Site settings updated successfully",
      });
    } catch (error) {
      console.error('Error updating site settings:', error);
      toast({
        title: "Error",
        description: "Failed to update site settings",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateAdminProfile = async (formData: FormData) => {
    setIsLoading(true);
    try {
      const userData = await settingsService.updateProfile(formData);
       localStorage.setItem('auth_user', JSON.stringify(userData));
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    setIsLoading(true);
    try {
      await settingsService.changePassword({
        currentPassword,
        newPassword,
      });
      toast({
        title: "Success",
        description: "Password changed successfully",
      });
    } catch (error) {
      console.error('Error changing password:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to change password",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: SettingsContextType = {
    siteSettings,
    isLoading,
    updateSiteSettings,
    updateAdminProfile,
    changePassword,
  };

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>;
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
