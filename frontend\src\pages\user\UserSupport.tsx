import React, { useState, useRef, useCallback, useEffect } from "react";
import UserLayout from "@/components/layout/UserLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogFooter,
} from "@/components/ui/dialog";
import supportService, { Ticket } from "@/services/supportService";
import {
  MessageSquare,
  FileText,
  HelpCircle,
  Mail,
  Phone,
  Image,
  Loader2,
  CheckCircle,
  Send,
  Paperclip,
  Eye,
  Clock,
  X
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { format } from "date-fns";

// Define types for form data
interface TicketFormData {
  subject: string;
  message: string;
}

const UserSupport = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [formData, setFormData] = useState<TicketFormData>({
    subject: "",
    message: ""
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [replyText, setReplyText] = useState("");
  const [isReplyDialogOpen, setIsReplyDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("create");
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Fetch user tickets
    fetchUserTickets();
  }, []);

  const fetchUserTickets = async () => {
    setIsLoading(true);
    try {
      const ticketsData = await supportService.getUserTickets();
      setTickets(ticketsData);
    } catch (error) {
      console.error("Error fetching tickets:", error);
      toast({
        title: "Error",
        description: "Failed to load your support tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    },
    []
  );

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
  }, []);

  const handleFileButtonClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData({
      subject: "",
      message: ""
    });
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, []);

  // Handle form submission
  const handleSubmitTicket = async (e: React.FormEvent) => {
    // Prevent default form submission behavior which causes page refresh
    e.preventDefault();

    // Validate form fields
    if (!formData.subject.trim() || !formData.message.trim()) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Set loading state
      setIsSubmitting(true);
      setIsSuccess(false);

      // Prepare data for API call
      const ticketData = {
        subject: formData.subject,
        message: formData.message,
        attachment: selectedFile
      };

      // Call the API to create a ticket
      await supportService.createTicket(ticketData);

      // Show success message
      toast({
        title: "Support ticket submitted",
        description: "We've received your request and will respond shortly.",
        variant: "default"
      });

      // Set success state and reset form
      setIsSuccess(true);
      resetForm();

      // Refresh tickets
      fetchUserTickets();

      // Switch to tickets tab
      setActiveTab("tickets");

    } catch (error) {
      console.error("Error submitting ticket:", error);
      toast({
        title: "Error",
        description: "Failed to submit ticket. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return "bg-green-100 text-green-800";
      case 'in-progress':
        return "bg-blue-100 text-blue-800";
      case 'resolved':
        return "bg-purple-100 text-purple-800";
      case 'closed':
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Function to handle ticket reply
  const handleTicketReply = async () => {
    if (!selectedTicket || !replyText.trim()) return;

    try {

      setIsSubmitting(true);
      await supportService.addResponse(selectedTicket._id, replyText);
      setReplyText('');

      toast({
        title: 'Success',
        description: 'Reply sent successfully'
      });


      // Refresh tickets to get updated data
      fetchUserTickets();

      // Update selected ticket
      const updatedTickets = await supportService.getUserTickets();
      const updatedTicket = updatedTickets.find(t => t._id === selectedTicket._id);
      if (updatedTicket) {
        setSelectedTicket(updatedTicket);
      }
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({
        title: 'Error',
        description: 'Failed to send reply',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if the current user can reply to the ticket
  const canReply = (ticket: Ticket) => {
    return ticket.status !== 'closed' && ticket.status !== 'resolved';
  };

  // Check if a message is from the current user
  const isFromCurrentUser = (userId: string) => {
    return userId === user?.userId;
  };

  return (
    <UserLayout>
      <div className="space-y-6 mx-auto py-10">
        <div>
          <h1 className="text-3xl font-bold">Support Tickets</h1>
          <p className="text-gray-500 mt-1">Submit and manage your support requests</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">Create New Ticket</TabsTrigger>
            <TabsTrigger value="tickets">Your Tickets</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="mt-6">
            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Quick Help Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Help</CardTitle>
                  <CardDescription>Common support options</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="mr-2 h-4 w-4" />
                    View Documentation
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <HelpCircle className="mr-2 h-4 w-4" />
                    Visit Knowledge Base
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Live Chat Support
                  </Button>
                </CardContent>
              </Card>

              {/* Contact Support Card */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Contact Support</CardTitle>
                  <CardDescription>Submit a support ticket</CardDescription>
                </CardHeader>
                <form onSubmit={handleSubmitTicket} noValidate>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="subject">Subject</Label>
                      <Input
                        id="subject"
                        name="subject"
                        placeholder="Briefly describe your issue"
                        value={formData.subject}
                        onChange={handleInputChange}
                        disabled={isSubmitting}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="message">Message</Label>
                      <Textarea
                        id="message"
                        name="message"
                        placeholder="Please provide details about your issue"
                        rows={5}
                        value={formData.message}
                        onChange={handleInputChange}
                        disabled={isSubmitting}
                        required
                      />
                    </div>
                  </CardContent>
                  <CardContent className="pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="attachment">Attachment (Optional, Images only)</Label>
                      <div className="flex items-center gap-2 flex-wrap">
                        <input
                          type="file"
                          id="attachment"
                          ref={fileInputRef}
                          accept="image/*"
                          onChange={handleFileChange}
                          disabled={isSubmitting}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleFileButtonClick}
                          disabled={isSubmitting}
                          className="flex items-center gap-2"
                        >
                          <Image className="h-4 w-4" />
                          {selectedFile ? 'Change Image' : 'Attach Image'}
                        </Button>
                        {selectedFile && (
                          <span className="text-sm text-muted-foreground truncate max-w-[200px] sm:max-w-none">
                            {selectedFile.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    {isSuccess ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="mr-2 h-5 w-5" />
                        <span>Ticket submitted successfully</span>
                      </div>
                    ) : (
                      <Button
                        type="submit"
                        className="bg-primary hover:bg-admin-secondary"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Submitting...
                          </>
                        ) : (
                          'Submit Ticket'
                        )}
                      </Button>
                    )}
                  </CardFooter>
                </form>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tickets" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Your Support Tickets</CardTitle>
                <CardDescription>View and manage your support tickets</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Loading your tickets...</span>
                  </div>
                ) : tickets.length === 0 ? (
                  <div className="text-center py-10">
                    <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No tickets found</h3>
                    <p className="text-gray-500 mb-6">You haven't submitted any support tickets yet.</p>
                    <Button
                      onClick={() => setActiveTab("create")}
                      className="bg-primary hover:bg-admin-secondary"
                    >
                      Create Your First Ticket
                    </Button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-2 font-medium">Subject</th>
                          <th className="text-left py-3 px-2 font-medium">Status</th>
                          <th className="text-left py-3 px-2 font-medium">Created</th>
                          <th className="text-left py-3 px-2 font-medium">Last Update</th>
                          <th className="text-right py-3 px-2 font-medium">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {tickets.map((ticket) => (
                          <tr key={ticket._id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-2">
                              <div className="font-medium">{ticket.subject}</div>
                              <div className="text-sm text-gray-500 truncate max-w-[250px]">
                                {ticket.message.length > 60
                                  ? `${ticket.message.substring(0, 60)}...`
                                  : ticket.message}
                              </div>
                            </td>
                            <td className="py-3 px-2">
                              <Badge
                                variant="outline"
                                className={`${getStatusColor(ticket.status)} border-0`}
                              >
                                {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                              </Badge>
                            </td>
                            <td className="py-3 px-2 whitespace-nowrap">
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="text-sm">
                                  {format(new Date(ticket.createdAt), 'MMM d, yyyy')}
                                </span>
                              </div>
                            </td>
                            <td className="py-3 px-2 whitespace-nowrap">
                              <div className="text-sm">
                                {format(new Date(ticket.updatedAt), 'MMM d, yyyy')}
                              </div>
                            </td>
                            <td className="py-3 px-2 text-right">
                              <Button
                                variant="outline"
                                size="sm"
                                className="ml-2"
                                onClick={() => {
                                  setSelectedTicket(ticket);
                                  setIsReplyDialogOpen(true);
                                }}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Alternative ways to reach us</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="flex items-start space-x-4">
                <div className="bg-admin-primary/10 p-3 rounded-full">
                  <Mail className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Email Support</h3>
                  <p className="text-gray-500 mt-1"><EMAIL></p>
                  <p className="text-sm text-gray-500 mt-2">
                    We typically respond within 24 hours
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="bg-admin-primary/10 p-3 rounded-full">
                  <Phone className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Phone Support</h3>
                  <p className="text-gray-500 mt-1">+****************</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Monday - Friday, 9AM-5PM EST
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chat-like Dialog for Ticket Replies */}
      <Dialog open={isReplyDialogOpen} onOpenChange={setIsReplyDialogOpen}>
        <DialogContent className="sm:max-w-3xl h-[80vh] max-h-[700px] flex flex-col p-0 gap-0 overflow-hidden">
          {selectedTicket && (
            <>
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white border-b">
                <div className="flex items-center justify-between p-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-white/20 p-2 rounded-lg">
                      <MessageSquare className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{selectedTicket.subject}</h3>
                      <div className="flex flex-wrap items-center text-sm text-white/80">
                        <div className="flex items-center mr-3">
                          <span className="font-medium">ID:</span>
                          <span className="ml-1">{selectedTicket._id.slice(-6)}</span>
                        </div>
                        <div className="flex items-center mr-3">
                          <span className="font-medium">Status:</span>
                          <span className="ml-1 bg-white/20 px-2 py-0.5 rounded-full text-xs">{selectedTicket.status}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium">Created:</span>
                          <span className="ml-1">{format(new Date(selectedTicket.createdAt), 'MMM d, yyyy')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                   
                </div>
              </div>

              {/* Chat area */}
              <div className="flex-1 overflow-y-auto p-4 bg-gray-100">
                <div className="space-y-4">
                  {/* Original ticket message */}
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10 mt-1">
                      <AvatarFallback className="bg-blue-800 text-white">
                        {user?.name?.charAt(0) || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">You</span>
                        <span className="text-xs text-gray-500">
                          {format(new Date(selectedTicket.createdAt), 'MMM d, yyyy • h:mm a')}
                        </span>
                      </div>
                      <div className="bg-blue-600 text-white rounded-tl-lg rounded-bl-lg rounded-br-lg p-3 shadow-sm mt-1 max-w-md">
                        <p>{selectedTicket.message}</p>
                        {selectedTicket.attachmentUrl && (
                          <div className="mt-2">
                            <a href={selectedTicket.attachmentUrl} target="_blank" rel="noopener noreferrer" className="text-white hover:underline flex items-center">
                              <img src={selectedTicket.attachmentUrl} alt="Attachment" className="max-h-40 max-w-full rounded-md border border-white/30" />
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Responses */}
                  {selectedTicket.responses && selectedTicket.responses.map((response) => {
                    const fromCurrentUser = isFromCurrentUser(response.createdBy);
                    return (
                      <div key={response._id} className={`flex items-start gap-3 ${fromCurrentUser ? 'justify-end' : ''}`}>
                        {!fromCurrentUser && (
                          <Avatar className="h-10 w-10 mt-1">
                            <AvatarFallback className="bg-gray-300 text-gray-600">
                              A
                            </AvatarFallback>
                          </Avatar>
                        )}

                        <div className="flex flex-col">
                          <div className={`flex items-center gap-2 ${fromCurrentUser ? 'justify-end' : ''}`}>
                            <span className="font-medium">
                              {fromCurrentUser ? 'You' : "Admin"}
                            </span>
                            <span className="text-xs text-gray-500">
                              {format(new Date(response.createdAt), 'MMM d, yyyy • h:mm a')}
                            </span>
                          </div>
                          <div
                            className={`${
                              fromCurrentUser
                                ? 'bg-blue-600 text-white rounded-tl-lg rounded-bl-lg rounded-br-lg ml-auto'
                                : 'bg-white rounded-tr-lg rounded-br-lg rounded-bl-lg'
                            } p-3 shadow-sm mt-1 max-w-md`}
                          >
                            <p>{response.message}</p>
                          </div>
                        </div>
                        {fromCurrentUser && (
                          <Avatar className="h-10 w-10 mt-1">
                            <AvatarFallback className="bg-blue-800 text-white">
                              {user?.name?.charAt(0) || "U"}
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Reply input area - only show if ticket is open */}
              {canReply(selectedTicket) ? (
                <div className="p-3 border-t bg-white">
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon" className="rounded-full h-10 w-10">
                      <Paperclip className="h-5 w-5" />
                    </Button>
                    <Input
                      className="flex-1"
                      placeholder="Type your reply..."
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleTicketReply();
                        }
                      }}
                    />
                    <Button
                      className="rounded-full h-10 w-10 bg-blue-600 hover:bg-blue-700"
                      onClick={handleTicketReply}
                      disabled={!replyText.trim() || isSubmitting}
                    >
                      {isSubmitting ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <Send className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="p-4 border-t bg-gray-50 text-center text-gray-500">
                  This ticket is {selectedTicket.status}. You cannot add more replies.
                </div>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </UserLayout>
  );
};

export default UserSupport;

