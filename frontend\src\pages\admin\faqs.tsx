import { useEffect, useState } from 'react';
import DashboardLayout from "@/components/layout/DashboardLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogClose, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Trash, Plus, AlertCircle } from "lucide-react";
import supportService, { FAQ, CreateFAQData } from '@/services/supportService';
import { useToast } from '@/hooks/use-toast';
import StatusBadge from '@/components/common/StatusBadge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Table, TableCell, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const FAQsPage = () => {
  const { toast } = useToast();

  // State for FAQs data
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for dialogs
  const [isFAQDialogOpen, setIsFAQDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // State for FAQ being edited or deleted
  const [currentFAQ, setCurrentFAQ] = useState<FAQ | null>(null);
  const [newFAQ, setNewFAQ] = useState({
    question: "",
    answer: "",
  });

  // Fetch FAQs on component mount
  useEffect(() => {
    fetchFAQs();
  }, []);

  const fetchFAQs = async () => {
    setIsLoading(true);
    try {
      const faqsData = await supportService.getAllFAQs();
      setFaqs(faqsData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load FAQs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handler for adding new FAQ
  const handleAddFAQ = async () => {
    try {
      // Add default category to meet API requirements
      const faqData: CreateFAQData = {
        question: newFAQ.question,
        answer: newFAQ.answer,
        category: "General"
      };

      const createdFAQ = await supportService.createFAQ(faqData);
      setFaqs([...faqs, createdFAQ]);
      setNewFAQ({ question: '', answer: '' });
      setIsFAQDialogOpen(false);
      toast({
        title: 'Success',
        description: 'FAQ created successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create FAQ',
        variant: 'destructive'
      });
    }
  };

  // Handler for editing FAQ
  const handleEditFAQ = async () => {
    if (!currentFAQ) return;

    try {
      const updateData: CreateFAQData = {
        question: currentFAQ.question,
        answer: currentFAQ.answer,
        category: currentFAQ.category || "General"
      };

      const updatedFAQ = await supportService.updateFAQ(currentFAQ._id, updateData);


      setFaqs(faqs.map(faq => faq._id === currentFAQ._id ? updatedFAQ : faq));
      setIsEditDialogOpen(false);
      setCurrentFAQ(null);
      toast({
        title: 'Success',
        description: 'FAQ updated successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update FAQ',
        variant: 'destructive'
      });
    }
  };

  // Handler for deleting FAQ
  const handleDeleteFAQ = async () => {
    if (!currentFAQ) return;

    try {
      await supportService.deleteFAQ(currentFAQ._id);
      setFaqs(faqs.filter(faq => faq._id !== currentFAQ._id));
      setIsDeleteDialogOpen(false);
      setCurrentFAQ(null);
      toast({
        title: 'Success',
        description: 'FAQ deleted successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete FAQ',
        variant: 'destructive'
      });
    }
  };

  // Open edit dialog with FAQ data
  const openEditDialog = (faq: FAQ) => {
    setCurrentFAQ(faq);
    setIsEditDialogOpen(true);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (faq: FAQ) => {
    setCurrentFAQ(faq);
    setIsDeleteDialogOpen(true);
  };

  // Handler for toggling FAQ status
  const handleFAQToggle = async (faqId: string) => {
    try {
      const updatedFAQ = await supportService.toggleFAQStatus(faqId);
      setFaqs(faqs.map(faq => faq._id === faqId ? updatedFAQ : faq));
      toast({
        title: 'Success',
        description: 'FAQ status updated'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update FAQ status',
        variant: 'destructive'
      });
    }
  };

  // Filter FAQs based on search query
  const filteredFAQs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <DashboardLayout>
      <div className="space-y-6 mx-auto py-10">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">FAQs Management</h1>
          <Button onClick={() => setIsFAQDialogOpen(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" /> Add New FAQ
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Manage FAQs</h2>
          <p className="text-gray-500 mb-6">Add, edit or remove frequently asked questions that appear on your website</p>

          <div className="mb-6">
            <Input
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-md"
            />
          </div>

          <div className="border rounded-md">

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Question</TableHead>
                  <TableHead>Answer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">Loading...</TableCell>
                  </TableRow>
                ) : filteredFAQs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center">No FAQs found</TableCell>
                  </TableRow>
                ) : (
                  filteredFAQs.map((faq) => (
                    <TableRow key={faq._id} >
                      <TableCell >{faq.question}</TableCell>
                      <TableCell>{faq.answer}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger>
                            <StatusBadge status={faq.isActive ? 'active' : 'inactive'} />
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => handleFAQToggle(faq._id)}>
                              {faq.isActive ? 'Set Inactive' : 'Set Active'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" onClick={() => openEditDialog(faq)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="hover:bg-destructive/10" onClick={() => openDeleteDialog(faq)}>
                            <Trash className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

          {/* {isLoading ? (
              <div className="p-8 text-center text-gray-500">Loading...</div>
            ) : filteredFAQs.length === 0 ? (
              <div className="p-8 text-center text-gray-500">No FAQs found</div>
            ) : (
              filteredFAQs.map((faq) => (
                <div key={faq._id} className="grid grid-cols-12 gap-4 p-4 border-b last:border-b-0 items-center">
                  <div className="col-span-5 truncate">{faq.question}</div>
                  <div className="col-span-5 truncate">{faq.answer}</div>
                  <div className="col-span-1">
                    <DropdownMenu>
                      <DropdownMenuTrigger>
                        <StatusBadge status={faq.isActive ? 'active' : 'inactive'} />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleFAQToggle(faq._id)}>
                          {faq.isActive ? 'Set Inactive' : 'Set Active'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="col-span-1 flex justify-end space-x-2">
                    <Button variant="ghost" size="icon" onClick={() => openEditDialog(faq)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="hover:bg-destructive/10" onClick={() => openDeleteDialog(faq)}>
                      <Trash className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          

      {/* Add New FAQ Dialog */}
          <Dialog open={isFAQDialogOpen} onOpenChange={setIsFAQDialogOpen}>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle>Add New FAQ</DialogTitle>
                <DialogDescription>
                  Create a new frequently asked question to display on your site.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <label htmlFor="question" className="text-sm font-medium">Question</label>
                  <Input
                    id="question"
                    placeholder="Enter the question"
                    value={newFAQ.question}
                    onChange={(e) => setNewFAQ({ ...newFAQ, question: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="answer" className="text-sm font-medium">Answer</label>
                  <Textarea
                    id="answer"
                    placeholder="Enter the answer"
                    value={newFAQ.answer}
                    onChange={(e) => setNewFAQ({ ...newFAQ, answer: e.target.value })}
                    rows={5}
                  />
                </div>
              </div>

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button onClick={handleAddFAQ}>Save FAQ</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit FAQ Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle>Edit FAQ</DialogTitle>
                <DialogDescription>
                  Update this frequently asked question.
                </DialogDescription>
              </DialogHeader>

              {currentFAQ && (
                <div className="space-y-4 py-2">
                  <div className="space-y-2">
                    <label htmlFor="edit-question" className="text-sm font-medium">Question</label>
                    <Input
                      id="edit-question"
                      placeholder="Enter the question"
                      value={currentFAQ.question}
                      onChange={(e) => setCurrentFAQ({ ...currentFAQ, question: e.target.value })}
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="edit-answer" className="text-sm font-medium">Answer</label>
                    <Textarea
                      id="edit-answer"
                      placeholder="Enter the answer"
                      value={currentFAQ.answer}
                      onChange={(e) => setCurrentFAQ({ ...currentFAQ, answer: e.target.value })}
                      rows={5}
                    />
                  </div>
                </div>
              )}

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button onClick={() => handleEditFAQ()}>Update FAQ</Button>
                {/* <Button onClick={()=>console.log('currentFAQ',currentFAQ)}>Update FAQ</Button> */}
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-500" />
                  Delete FAQ
                </DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this FAQ? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>

              {currentFAQ && (
                <div className="py-3">
                  <h3 className="font-medium">Question:</h3>
                  <p className="mt-1 text-sm text-gray-500">{currentFAQ.question}</p>
                </div>
              )}

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button variant="destructive" onClick={handleDeleteFAQ}>Yes, delete</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </DashboardLayout>
        );
};

        export default FAQsPage; 