import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import UserLayout from "@/components/layout/UserLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { PlayCircle, Search, Filter, ArrowUpDown, X } from "lucide-react";
import videoService, { VideoTutorial } from "@/services/videoService";

const VideoTutorials = () => {
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const [videos, setVideos] = useState<VideoTutorial[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("newest");
  const [activeVideo, setActiveVideo] = useState<string | null>(null);
  const [fullscreenVideo, setFullscreenVideo] = useState<VideoTutorial | null>(null);

  // Parse video ID from URL query parameters
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const videoId = queryParams.get('video');
    
    if (videoId) {
      setActiveVideo(videoId);
      fetchVideoById(videoId);
    }
  }, [location.search]);

  useEffect(() => {
    fetchVideos();
    fetchCategories();
  }, []);

  const fetchVideos = async () => {
    setIsLoading(true);
    try {
      const response = await videoService.getAllVideos();
      setVideos(response);
    } catch (error) {
      console.error("Error fetching video tutorials:", error);
      toast({
        title: "Error",
        description: "Failed to load video tutorials",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await videoService.getCategories();
      setCategories(response);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchVideoById = async (id: string) => {
    try {
      const video = await videoService.getVideoById(id);
      setFullscreenVideo(video);
    } catch (error) {
      console.error("Error fetching video:", error);
      toast({
        title: "Error",
        description: "Failed to load the selected video",
        variant: "destructive",
      });
    }
  };

  // Filter videos based on search query and category
  const filteredVideos = videos.filter(video => {
    const matchesSearch = 
      video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      video.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      video.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || video.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort videos based on selected sorting option
  const sortedVideos = [...filteredVideos].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case "oldest":
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case "title-asc":
        return a.title.localeCompare(b.title);
      case "title-desc":
        return b.title.localeCompare(a.title);
      default:
        return 0;
    }
  });

  // Format duration from seconds to MM:SS
  const formatDuration = (duration: string): string => {
    return duration;
  };

  // Handle video click to show embedded player
  const handleVideoClick = (videoId: string) => {
    if (activeVideo === videoId) {
      setActiveVideo(null);
      // Remove video parameter from URL
      navigate('/user/video-tutorials', { replace: true });
    } else {
      setActiveVideo(videoId);
      // Add video parameter to URL
      navigate(`/user/video-tutorials?video=${videoId}`, { replace: true });
    }
  };

  // Close fullscreen video
  const closeFullscreenVideo = () => {
    setFullscreenVideo(null);
    setActiveVideo(null);
    navigate('/user/video-tutorials', { replace: true });
  };

  return (
    <UserLayout>
      {fullscreenVideo ? (
        <div className="space-y-6 mx-auto py-10">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">{fullscreenVideo.title}</h1>
            <Button variant="ghost" size="icon" onClick={closeFullscreenVideo}>
              <X className="h-6 w-6" />
            </Button>
          </div>
          
          <div className="aspect-video w-full mb-8">
            <iframe
              className="w-full h-full"
              src={`https://www.youtube.com/embed/${fullscreenVideo.youtubeId}?autoplay=1`}
              title={fullscreenVideo.title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
          
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold">Description</h2>
              <p className="mt-2 text-gray-700">{fullscreenVideo.description}</p>
            </div>
            
            <div>
              <h2 className="text-xl font-semibold">Category</h2>
              <p className="mt-2 text-gray-700">{fullscreenVideo.category}</p>
            </div>
            
            {fullscreenVideo.tags && fullscreenVideo.tags.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold">Tags</h2>
                <div className="flex flex-wrap gap-2 mt-2">
                  {fullscreenVideo.tags.map(tag => (
                    <span 
                      key={tag} 
                      className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="mt-8">
            <Button 
              variant="outline"
              onClick={() => {
                fetchVideos();
                closeFullscreenVideo();
              }}
            >
              Back to All Tutorials
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-6 mx-auto py-10">
          <div>
            <h1 className="text-3xl font-bold">Video Tutorials</h1>
            <p className="text-gray-500 mt-1">Learn how to use our platform with step-by-step video guides</p>
          </div>

          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search videos..."
                className="pl-8 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <ArrowUpDown className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="title-asc">Title (A-Z)</SelectItem>
                  <SelectItem value="title-desc">Title (Z-A)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Videos Grid */}
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
            </div>
          ) : sortedVideos.length === 0 ? (
            <div className="text-center py-16">
              <PlayCircle className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No videos found</h3>
              <p className="text-gray-500">
                {searchQuery
                  ? `No results matching "${searchQuery}"`
                  : "There are no video tutorials available at the moment."}
              </p>
            </div>
          ) : (
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {sortedVideos.map((video) => (
                <Card key={video._id} className="overflow-hidden">
                  <div className="relative aspect-video bg-gray-100">
                    {activeVideo === video._id ? (
                      <iframe
                        className="absolute inset-0 w-full h-full"
                        src={`https://www.youtube.com/embed/${video.youtubeId}?autoplay=1`}
                        title={video.title}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      ></iframe>
                    ) : (
                      <>
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover"
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute inset-0 w-full h-full bg-black/30 flex items-center justify-center rounded-none"
                          onClick={() => handleVideoClick(video._id)}
                        >
                          <PlayCircle className="h-16 w-16 text-white" />
                        </Button>
                        <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 text-xs rounded">
                          {formatDuration(video.duration)}
                        </div>
                      </>
                    )}
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg line-clamp-2" title={video.title}>
                        {video.title}
                      </CardTitle>
                    </div>
                    <CardDescription className="line-clamp-1 mt-1">
                      {video.category}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 line-clamp-2">{video.description}</p>
                  </CardContent>
                  <CardFooter className="pt-0 flex gap-1 flex-wrap">
                    {video.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </UserLayout>
  );
};

export default VideoTutorials; 