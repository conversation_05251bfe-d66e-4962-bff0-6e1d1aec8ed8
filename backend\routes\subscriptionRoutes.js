import express from 'express';
import { protectUserRoutes as protect, protectAdminRoutes as protectAdmin } from '../middleware/authMiddleware.js';
import {
    getCurrentSubscription,
    getSubscriptionHistory,
    getPaymentHistory,
    getDashboardSummary,
    getAvailablePlans,
    cancelSubscription,
    getUserPaymentHistory,
    calculateUpgradeAmount,
    handleSubscriptionUpgrade,
    getUserSubscriptionPaymentHistory
} from '../controllers/subscriptionController.js';

const router = express.Router();

// @route   GET /api/subscriptions
// @desc    Get current user's subscription
// @access  Private
router.get('/', protect, async (req, res) => {
    try {
        const { Subscription } = await import('../models/index.js');

        const subscription = await Subscription.findOne({ user: req.user._id })
            .populate('subscriptionPlan')
            .sort({ createdAt: -1 });

        if (!subscription) {
            return res.status(404).json({ message: 'No subscription found' });
        }

        res.status(200).json(subscription);
    } catch (error) {
        console.error('Error fetching subscription:', error);
        res.status(500).json({ message: 'Server error', error: error.message });
    }
});

// @route   GET /api/subscriptions/history
// @desc    Get current user's subscription history
// @access  Private

// router.get('/history', protect, async (req, res) => {
//     try {
//         const { Subscription } = await import('../models/index.js');
//         console.log('req.user', req.user);
//         const subscriptions = await Subscription.find({ user: req.user._id })
//             .populate('subscriptionPlan')
//             .sort({ createdAt: -1 });

//         console.log('subscriptions', subscriptions);

//         res.status(200).json(subscriptions);
//     } catch (error) {
//         console.error('Error fetching subscription history:', error);
//         res.status(500).json({ message: 'Server error', error: error.message });
//     }
// });

// @route   GET /api/subscriptions/invoices
// @desc    Get current user's invoices
// @access  Private
router.get('/invoices', protect, async (req, res) => {
    try {
        const { Subscription } = await import('../models/index.js');

        const subscriptions = await Subscription.find({ user: req.user._id });

        // Extract invoices from all subscriptions
        const invoices = subscriptions.flatMap(sub => sub.invoices || []);

        // Sort by date (newest first)
        invoices.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        res.status(200).json(invoices);
    } catch (error) {
        console.error('Error fetching invoices:', error);
        res.status(500).json({ message: 'Server error', error: error.message });
    }
});

// Get current user's subscription
router.get('/current', protect, getCurrentSubscription);

// Get user's subscription history
router.get('/history', protect, getSubscriptionHistory);
router.get('/user-payment-history', protect, getUserPaymentHistory);
router.get('/user-subscription-payment-history/:userId', protectAdmin, getUserSubscriptionPaymentHistory);

// Get user's payment history
router.get('/payment-history', getPaymentHistory);

// Get dashboard summary with subscription and payment info
router.get('/dashboard-summary', protect, getDashboardSummary);

// Get available subscription plans
router.get('/available-plans', protect, getAvailablePlans);

// Calculate upgrade amount
router.get('/calculate-upgrade/:planId', protect, calculateUpgradeAmount);

// Handle subscription upgrade
router.post('/upgrade/:planId', protect, handleSubscriptionUpgrade);

// Cancel subscription
router.post('/cancel', protect, cancelSubscription);

export default router; 