import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import connectDB from './config/db.js';
import fs from 'fs';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';

// Import routes
import userRoutes from './routes/userRoutes.js';
import adminRoutes from './routes/adminRoutes.js';
import subscriptionPlanRoutes from './routes/subscriptionPlanRoutes.js';
import stripeRoutes from './routes/stripeRoutes.js';
import apiKeyRoutes from './routes/apiKeyRoutes.js';
import settingsRoutes from './routes/settingsRoutes.js';
import aiModelSettingsRoutes from './routes/aiModelSettingsRoutes.js';
import supportRoutes from './routes/supportRoutes.js';
import subscriptionRoutes from './routes/subscriptionRoutes.js';
import creditPlanRoutes from './routes/creditPlanRoutes.js';
import videoTutorialsRoutes from './routes/videoTutorialsRoutes.js';
import adminVideoTutorialRoutes from './routes/adminVideoTutorialRoutes.js';
import creditPaymentRoutes from './routes/creditPaymentRoutes.js';
import aiInterviewRoutes from './routes/aiInterviewRoutes.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
connectDB();

const app = express();

// Get directory name (ESM equivalent of __dirname)
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://js.stripe.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https://ui-avatars.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      connectSrc: ["'self'", "https://*.stripe.com", "https://api.clerk.dev"]
    }
  },
  // Enable XSS Protection
  xssFilter: true,
  // Prevent clickjacking
  frameguard: { action: 'deny' },
  // Disable MIME type sniffing
  noSniff: true,
  // Sets Referrer-Policy header
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  // HSTS configuration
  hsts: {
    maxAge: 15552000, // 180 days in seconds
    includeSubDomains: true,
    preload: true
  },
  // Disable caching for sensitive routes
  noCache: false, // Enable selectively for certain routes
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// Middleware
// app.use(cors());http://localhost:54321'
// CORS Configuration

const corsOptions = {
  origin: ['http://localhost:8016', 'http://localhost:3016', 'http://localhost:54321', 'http://***********:3016', 'https://interview.laxmisoft.net', 'https://interviewapi.laxmisoft.net',],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'stripe-signature'],
  exposedHeaders: ['Content-Range', 'X-Content-Range']
};
app.use(cors(corsOptions));

// Define rate limiters
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per IP
  standardHeaders: true,
  message: { success: false, message: 'Too many login attempts, please try again later' }
});

const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute
  standardHeaders: true,
  message: { success: false, message: 'Too many requests, please try again later' }
});

// Special handling for Stripe webhook route
app.use('/api/stripe/webhook', express.raw({ type: 'application/json' }));

// Standard JSON body parser for all other routes
app.use(express.json());
app.use(morgan('dev'));

// Cookie parser for handling auth cookies
app.use(cookieParser());

// Path normalization middleware for static files
const normalizePathMiddleware = (req, res, next) => {
  // Normalize URL path - replace backslashes with forward slashes
  req.url = req.url.replace(/\\/g, '/');
  next();
};

// Serve static files from uploads directory with path normalization
app.use('/uploads', normalizePathMiddleware, express.static(path.join(__dirname, 'uploads')));

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads');
const resumesDir = path.join(uploadsDir, 'resumes');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

if (!fs.existsSync(resumesDir)) {
  fs.mkdirSync(resumesDir, { recursive: true });
}

// Apply rate limiting to auth routes
app.use('/api/users/login', authLimiter);
app.use('/api/admins/login', authLimiter);

// Apply general API rate limiting
app.use('/api', apiLimiter);

// Routes
app.use('/api/users', userRoutes);
app.use('/api/admins', adminRoutes);
app.use('/api/subscription-plans', subscriptionPlanRoutes);
app.use('/api/stripe', stripeRoutes);
app.use('/api/admin/api-keys', apiKeyRoutes);
app.use('/api/admin/settings', settingsRoutes);
app.use('/api/admin/ai-model-settings', aiModelSettingsRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/credit-plans', creditPlanRoutes);
app.use('/api/video-tutorials', videoTutorialsRoutes);
app.use('/api/admin/video-tutorials', adminVideoTutorialRoutes);
app.use('/api/admin/credit-payments', creditPaymentRoutes);
app.use('/api/interview/session', aiInterviewRoutes);

// Root route
app.get('/', (req, res) => {
  res.send('API is running...');
});

// Error handling middleware
app.use((err, req, res, next) => {
  // Log the error for server-side debugging, but not in tests
  if (process.env.NODE_ENV !== 'test') {
    console.error(err.stack);
  }

  // Determine if we're in development mode
  const isDev = process.env.NODE_ENV === 'development';

  // In production, never reveal detailed error information
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: isDev ? err.message : 'Internal server error',
  });
});

// Start server
const PORT = process.env.PORT || 8016;
app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
});
