import User from '../models/userModel.js';
import Subscription from '../models/subscriptionModel.js';
import SubscriptionPlan from '../models/subscriptionPlanModel.js';
import SubscriptionTransaction from '../models/subscriptionTransaction.js';
import { stripe } from '../services/stripeService.js';

// @desc    Get current user subscription
// @route   GET /api/subscriptions/current
// @access  Private
export const getCurrentSubscription = async (req, res) => {
    try {
        const userId = req.user._id;

        // Get the user with their subscription info
        const user = await User.findById(userId).populate({
            path: 'subscription.planId',
            model: 'SubscriptionPlan'
        });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user has an active subscription
        if (!user.subscription || !user.subscription.status || user.subscription.status === 'inactive') {
            return res.status(200).json({
                success: true,
                hasActiveSubscription: false,
                message: 'User has no active subscription'
            });
        }

        // Get the subscription record
        let subscriptionRecord = null;
        if (user.subscription.subscriptionId) {
            subscriptionRecord = await Subscription.findById(user.subscription.subscriptionId);
        }
        console.log('user...', user);
        // Format subscription data
        const subscriptionData = {
            status: user.subscription.status,
            planName: user.subscription.planId ? user.subscription.planId.name : 'Unknown Plan',
            planId: user.subscription.planId ? user.subscription.planId._id : null,
            startDate: user.subscription.startDate,
            endDate: user.subscription.endDate,
            currentPeriodStart: user.subscription.currentPeriodStart,
            currentPeriodEnd: user.subscription.currentPeriodEnd,
            stripeSubscriptionId: user.subscription.stripeSubscriptionId,
            autoRenew: user.subscription.autoRenew !== false,
            cancelAtPeriodEnd: user.subscription.cancelAtPeriodEnd === true,
            price: user.subscription.planId ? user.subscription.planId.price : 0,
            currency: user.subscription.planId ? user.subscription.planId.currency : 'USD',
            subscriptionCredits: user.subscriptionCredits || 0,
            creditsPurchased: user.creditsPurchased || 0,
            billingInterval: user.subscription.planId ? user.subscription.planId.billingInterval : 'monthly',
            subscriptionRecord: subscriptionRecord
        };

        res.status(200).json({
            success: true,
            hasActiveSubscription: true,
            subscription: subscriptionData
        });
    } catch (error) {
        console.error('Error getting current subscription:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting subscription data',
            error: error.message
        });
    }
};

// @desc    Get subscription history
// @route   GET /api/subscriptions/history
// @access  Private
export const getSubscriptionHistory = async (req, res) => {
    try {
        const userId = req.user._id;

        // Get all subscription records for this user
        const subscriptions = await Subscription.find({
            userId: userId
        }).sort({ createdAt: -1 }).populate('planId');

        if (!subscriptions || subscriptions.length === 0) {
            return res.status(200).json({
                success: true,
                history: [],
                message: 'No subscription history found'
            });
        }

        console.log('subscriptions----------', subscriptions);

        // Format the history data
        const formattedHistory = subscriptions.map(sub => ({
            id: sub._id,
            eventType: sub.eventType,
            status: sub.status,
            planName: sub.planId ? sub.planId.name : 'Unknown Plan',
            startDate: sub.startDate,
            endDate: sub.endDate,
            amount: sub.amount,
            currency: sub.currency,
            stripeSubscriptionId: sub.stripeSubscriptionId,
            createdAt: sub.createdAt
        }));

        res.status(200).json({
            success: true,
            history: formattedHistory
        });
    } catch (error) {
        console.error('Error getting subscription history:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting subscription history',
            error: error.message
        });
    }
};

// @desc    Get user payment history
// @route   GET /api/subscriptions/user-payment-history
// @access  Private
export const getUserPaymentHistory = async (req, res) => {
    try {
        // Get pagination parameters from query
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Get total count for pagination
        const total = await SubscriptionTransaction.countDocuments({
            userId: req.user._id
        });

        // Get transaction records with pagination
        const transactions = await SubscriptionTransaction.find({
            userId: req.user._id
        })
            .sort({ transactionDate: -1 })
            .skip(skip)
            .limit(limit)
            .populate('planId')
            .populate('userId');

        // Format the transaction data
        const formattedTransactions = transactions.map(transaction => ({
            id: transaction._id,
            userId: transaction.userId?._id,
            userName: transaction.userId?.name || 'Unknown User',
            userEmail: transaction.userId?.email || 'Unknown Email',
            userAvatar: transaction.userId?.avatar || '',
            amount: transaction.amount,
            currency: transaction.currency,
            status: transaction.status,
            planName: transaction.planName || (transaction.planId ? transaction.planId.name : 'Unknown Plan'),
            invoiceId: transaction.invoiceId,
            stripePaymentId: transaction.stripePaymentId,
            transactionDate: transaction.transactionDate,
            CustomerId: transaction.userId.subscription.stripeCustomerId,
            stripeSubscriptionId: transaction.userId.subscription.stripeSubscriptionId,
            subscriptionCredits: transaction.userId.subscriptionCredits || 0,
        }));

        res.status(200).json({
            success: true,
            payments: formattedTransactions,
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('Error getting all payment histories:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting all payment histories',
            error: error.message
        });
    }
};

// @desc    Get user subscription payment history
// @route   GET /api/subscriptions/user-subscription-payment-history/userId
// @access  Private
export const getUserSubscriptionPaymentHistory = async (req, res) => {
    const userId = req.params.userId;
    try {
        // Get pagination parameters from query
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Get total count for pagination
        const total = await SubscriptionTransaction.countDocuments({
            userId: userId
        });

        // Get transaction records with pagination
        const transactions = await SubscriptionTransaction.find({
            userId: userId
        })
            .sort({ transactionDate: -1 })
            .skip(skip)
            .limit(limit)
            .populate('planId')
            .populate('userId')
            .populate('subscriptionRecordId');

        console.log('transactions ---------- transactions', transactions[0]);
        // Format the transaction data
        const formattedTransactions = transactions.map(transaction => ({
            id: transaction._id,
            userId: transaction.userId?._id,
            userName: transaction.userId?.name || 'Unknown User',
            userEmail: transaction.userId?.email || 'Unknown Email',
            userAvatar: transaction.userId?.avatar || '',
            amount: transaction.amount,
            currency: transaction.currency,
            status: transaction.status,
            planName: transaction.planName || (transaction.planId ? transaction.planId.name : 'Unknown Plan'),
            invoiceId: transaction.invoiceId,
            stripePaymentId: transaction.stripePaymentId,
            transactionDate: transaction.transactionDate,
            CustomerId: transaction.userId.subscription.stripeCustomerId,
            stripeSubscriptionId: transaction.userId.subscription.stripeSubscriptionId,
            subscriptionCredits: transaction.userId.subscriptionCredits || 0,
            // New fields from Subscription model
            eventType: transaction.subscriptionRecordId?.eventType || 'unknown',
            autoRenew: transaction.subscriptionRecordId?.autoRenew !== false,
            startDate: transaction.subscriptionRecordId?.startDate || transaction.transactionDate,
            subscriptionStatus: transaction.subscriptionRecordId?.status || 'unknown',
            endDate: transaction.subscriptionRecordId?.endDate || transaction.transactionDate,
            billingInterval: transaction.subscriptionRecordId?.planId?.billingInterval || 'daily',
        }));

        res.status(200).json({
            success: true,
            payments: formattedTransactions,
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
        });
    } catch (error) {
        console.error('Error getting all payment histories:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting all payment histories',
            error: error.message
        });
    }
};

// @desc    Get payment history
// @route   GET /api/subscriptions/payment-history
// @access  Private
export const getPaymentHistory = async (req, res) => {
    try {
        // Get all transaction records from all users
        const transactions = await SubscriptionTransaction.find({})
            .sort({ transactionDate: -1 })
            .populate('planId')
            .populate('userId'); // optional: if you want to include user info

        // Format the transaction data
        const formattedTransactions = transactions.map(transaction => ({
            id: transaction._id,
            userId: transaction.userId?._id,
            userName: transaction.userId?.name || 'Unknown User',
            userEmail: transaction.userId?.email || 'Unknown Email',
            userAvatar: transaction.userId?.avatar || '',
            amount: transaction.amount,
            currency: transaction.currency,
            status: transaction.status,
            planName: transaction.planName || (transaction.planId ? transaction.planId.name : 'Unknown Plan'),
            invoiceId: transaction.invoiceId,
            stripePaymentId: transaction.stripePaymentId,
            transactionDate: transaction.transactionDate,
            CustomerId: transaction.userId.subscription.stripeCustomerId,
            stripeSubscriptionId: transaction.userId.subscription.stripeSubscriptionId,
        }));

        res.status(200).json({
            success: true,
            payments: formattedTransactions
        });
    } catch (error) {
        console.error('Error getting all payment histories:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting all payment histories',
            error: error.message
        });
    }
};


// @desc    Get user dashboard summary
// @route   GET /api/subscriptions/dashboard-summary
// @access  Private
export const getDashboardSummary = async (req, res) => {
    try {
        const userId = req.user._id;

        // Get the user with subscription info
        const user = await User.findById(userId).populate({
            path: 'subscription.planId',
            model: 'SubscriptionPlan'
        });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Get the most recent payment
        const latestPayment = await SubscriptionTransaction.findOne({
            userId: userId,
            status: 'success'
        }).sort({ transactionDate: -1 });

        // Get the last 5 transactions for recent activity
        const recentActivity = await SubscriptionTransaction.find({
            userId: userId
        }).sort({ transactionDate: -1 }).limit(5);

        // Format subscription data for the dashboard
        const subscriptionData = user.subscription && user.subscription.status === 'active' ? {
            status: user.subscription.status,
            planName: user.subscription.planId ? user.subscription.planId.name : 'Unknown Plan',
            startDate: user.subscription.startDate,
            endDate: user.subscription.endDate,
            price: user.subscription.planId ? user.subscription.planId.price : 0,
            currency: user.subscription.planId ? user.subscription.planId.currency : 'USD',
            renewsOn: user.subscription.currentPeriodEnd,
            autoRenew: user.subscription.autoRenew !== false,
            credits: user.credits
        } : null;

        // Format the most recent payment
        const latestPaymentData = latestPayment ? {
            amount: latestPayment.amount,
            currency: latestPayment.currency,
            date: latestPayment.transactionDate,
            planName: latestPayment.planName || 'Subscription',
            nextPaymentDate: user.subscription ? user.subscription.currentPeriodEnd : null
        } : null;

        // Format recent activity
        const recentActivityData = recentActivity.map(activity => ({
            id: activity._id,
            type: activity.metadata && activity.metadata.type ? activity.metadata.type : 'payment',
            description: activity.planName || 'Subscription Payment',
            amount: activity.amount,
            currency: activity.currency,
            status: activity.status,
            date: activity.transactionDate
        }));

        res.status(200).json({
            success: true,
            dashboardSummary: {
                subscription: subscriptionData,
                latestPayment: latestPaymentData,
                recentActivity: recentActivityData,
                credits: user.credits
            }
        });
    } catch (error) {
        console.error('Error getting dashboard summary:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting dashboard summary',
            error: error.message
        });
    }
};

// @desc    Get available subscription plans
// @route   GET /api/subscriptions/available-plans
// @access  Private
export const getAvailablePlans = async (req, res) => {
    try {
        // Get all active subscription plans
        const plans = await SubscriptionPlan.find({ isActive: true }).sort({ price: 1 });


        // Format the plans data
        const formattedPlans = plans.map(plan => ({

            id: plan._id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            currency: plan.currency,
            billingInterval: plan.billingInterval,
            intervalCount: plan.intervalCount,
            features: plan.features,
            subscriptionCredits: plan.subscriptionCredits,
            priceId: plan.priceId
        }));
        console.log('formattedPlans...', formattedPlans);
        res.status(200).json({
            success: true,
            plans: formattedPlans
        });
    } catch (error) {
        console.error('Error getting available plans:', error);
        res.status(500).json({
            success: false,
            message: 'Server error getting available plans',
            error: error.message
        });
    }
};

// @desc    Cancel subscription
// @route   POST /api/subscriptions/cancel
// @access  Private
export const cancelSubscription = async (req, res) => {
    try {
        const userId = req.user._id;
        const { cancelImmediately = false } = req.body;

        // Get user with subscription data
        const user = await User.findById(userId).populate('subscription.planId');

        if (!user || !user.subscription || !user.subscription.stripeSubscriptionId) {
            return res.status(400).json({
                success: false,
                message: 'No active subscription found'
            });
        }
        console.log('user...******', user);
        // Import stripe service
        const { stripe } = await import('../services/stripeService.js');

        let stripeSubscription;

        if (cancelImmediately) {
            // Cancel subscription immediately
            stripeSubscription = await stripe.subscriptions.cancel(user.subscription.stripeSubscriptionId);

            // Update user record
            user.subscription.status = 'canceled';
            user.subscription.endDate = new Date();
        } else {
            // Cancel at period end
            stripeSubscription = await stripe.subscriptions.update(user.subscription.stripeSubscriptionId, {
                cancel_at_period_end: true
            });

            // Update user record
            user.subscription.cancelAtPeriodEnd = true;
        }

        // Create a subscription history record for the cancellation
        const currentDate = new Date();
        const subscriptionRecord = new Subscription({
            userId: user._id,
            planId: user.subscription.planId,
            stripeSubscriptionId: user.subscription.stripeSubscriptionId,
            eventType: 'canceled',
            status: 'canceled',
            startDate: user.subscription.startDate || user.subscription.currentPeriodStart || currentDate,
            endDate: cancelImmediately ? currentDate : user.subscription.currentPeriodEnd || currentDate,
            autoRenew: false,
            metadata: {
                cancelReason: 'user_requested',
                cancelImmediately: cancelImmediately,
                previousSubscriptionRecordId: user.subscription.subscriptionId,
                cancelAtPeriodEnd: !cancelImmediately
            }
        });

        const savedSubscription = await subscriptionRecord.save();
        console.log('savedSubscription...', savedSubscription);
        // Update the user's subscription reference to point to this new record
        user.subscription.subscriptionId = savedSubscription._id;
        await user.save();

        res.status(200).json({
            success: true,
            message: cancelImmediately ?
                'Subscription canceled successfully' :
                'Subscription will be canceled at the end of the billing period'
        });
    } catch (error) {
        console.error('Error canceling subscription:', error);
        res.status(500).json({
            success: false,
            message: 'Server error canceling subscription',
            error: error.message
        });
    }
};

// @desc    Calculate the upgrade amount for subscription plan
// @route   GET /api/subscriptions/calculate-upgrade/:planId
// @access  Private
export const calculateUpgradeAmount = async (req, res) => {
    try {
        const userId = req.user._id;
        const { planId } = req.params;

        // Get the user with their subscription info
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user has an active subscription
        if (!user.subscription || !user.subscription.planId || user.subscription.status !== 'active') {
            return res.status(400).json({
                success: false,
                message: 'No active subscription found to upgrade from'
            });
        }

        // Get the target plan
        const targetPlan = await SubscriptionPlan.findById(planId);
        if (!targetPlan) {
            return res.status(404).json({
                success: false,
                message: 'Target subscription plan not found'
            });
        }

        // Get the current plan
        const currentPlan = await SubscriptionPlan.findById(user.subscription.planId);
        if (!currentPlan) {
            return res.status(404).json({
                success: false,
                message: 'Current subscription plan not found'
            });
        }

        // Check if this is a valid upgrade (new plan price > current plan price)
        if (targetPlan.price <= currentPlan.price) {
            return res.status(400).json({
                success: false,
                message: 'Cannot upgrade to a plan with equal or lower price'
            });
        }

        // Calculate the upgrade amount (difference between the plans)
        const upgradeAmount = targetPlan.price - currentPlan.price;

        res.status(200).json({
            success: true,
            currentPlan: {
                id: currentPlan._id,
                name: currentPlan.name,
                price: currentPlan.price,
                credits: currentPlan.subscriptionCredits
            },
            targetPlan: {
                id: targetPlan._id,
                name: targetPlan.name,
                price: targetPlan.price,
                credits: targetPlan.subscriptionCredits
            },
            upgradeAmount: upgradeAmount,
            currency: targetPlan.currency || 'USD'
        });
    } catch (error) {
        console.error('Error calculating upgrade amount:', error);
        res.status(500).json({
            success: false,
            message: 'Server error calculating upgrade amount',
            error: error.message
        });
    }
};

// @desc    Handle subscription upgrade
// @route   POST /api/subscriptions/upgrade/:planId
// @access  Private
export const handleSubscriptionUpgrade = async (req, res) => {
    try {
        const userId = req.user._id;
        const { planId } = req.params;

        // Get the user with their subscription info
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user has an active subscription
        if (!user.subscription || !user.subscription.planId || user.subscription.status !== 'active') {
            return res.status(400).json({
                success: false,
                message: 'No active subscription found to upgrade from'
            });
        }

        // Get the target plan
        const targetPlan = await SubscriptionPlan.findById(planId);
        if (!targetPlan) {
            return res.status(404).json({
                success: false,
                message: 'Target subscription plan not found'
            });
        }

        // Get the current plan
        const currentPlan = await SubscriptionPlan.findById(user.subscription.planId);
        if (!currentPlan) {
            return res.status(404).json({
                success: false,
                message: 'Current subscription plan not found'
            });
        }

        // Check if this is a valid upgrade (new plan price > current plan price)
        if (targetPlan.price <= currentPlan.price) {
            return res.status(400).json({
                success: false,
                message: 'Cannot upgrade to a plan with equal or lower price'
            });
        }

        // Calculate the upgrade amount (difference between the plans)
        const upgradeAmount = targetPlan.price - currentPlan.price;

        // Create a checkout session for the upgrade amount
        const session = await stripe.checkout.sessions.create({
            mode: 'payment',
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: targetPlan.currency.toLowerCase() || 'usd',
                        product_data: {
                            name: `Upgrade to ${targetPlan.name}`,
                            description: `Upgrade from ${currentPlan.name} to ${targetPlan.name}`
                        },
                        unit_amount: Math.round(upgradeAmount * 100), // Convert to cents
                    },
                    quantity: 1,
                },
            ],
            success_url: `${process.env.FRONTEND_URL}/user/subscription?success=true&upgrade=true`,
            cancel_url: `${process.env.FRONTEND_URL}/user/subscription?canceled=true`,
            customer_email: user.email,
            client_reference_id: userId.toString(),
            metadata: {
                userId: userId.toString(),
                type: 'subscription_upgrade',
                currentPlanId: currentPlan._id.toString(),
                targetPlanId: targetPlan._id.toString(),
                upgradeAmount: upgradeAmount.toString(),
                currentPlanCredits: currentPlan.subscriptionCredits.toString(),
                targetPlanCredits: targetPlan.subscriptionCredits.toString()
            }
        });

        res.status(200).json({
            success: true,
            sessionId: session.id,
            url: session.url
        });
    } catch (error) {
        console.error('Error handling subscription upgrade:', error);
        res.status(500).json({
            success: false,
            message: 'Server error handling subscription upgrade',
            error: error.message
        });
    }
};