import { api } from '../lib/axios';

export interface SubscriptionPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billingInterval: 'daily' | 'weekly' | 'monthly' | 'yearly';
    intervalCount: number;
    isActive: boolean;
    features: string[];
    credits: number;
    productId: string;
    priceId: string;
    planId: string;
    createdAt: string;
    updatedAt: string;
}

export interface UserSubscription {
    status: string;
    planName: string;
    planId: string;
    startDate: string;
    endDate: string;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    stripeSubscriptionId: string;
    autoRenew: boolean;
    cancelAtPeriodEnd: boolean;
    price: number;
    currency: string;
    credits: number;
    billingInterval: string;
}

export interface SubscriptionHistory {
    id: string;
    eventType: string;
    status: string;
    planName: string;
    startDate: string;
    endDate: string;
    amount: number;
    currency: string;
    stripeSubscriptionId: string;
    createdAt: string;
}

export interface PaymentHistory {
    id: string;
    amount: number;
    currency: string;
    status: string;
    planName: string;
    invoiceId: string;
    transactionDate: string;
    createdAt: string;
}

export interface DashboardSummary {
    subscription: {
        status: string;
        planName: string;
        startDate: string;
        endDate: string;
        price: number;
        currency: string;
        renewsOn: string;
        autoRenew: boolean;
        credits: number;
    } | null;
    latestPayment: {
        amount: number;
        currency: string;
        date: string;
        planName: string;
        nextPaymentDate: string | null;
    } | null;
    recentActivity: Array<{
        id: string;
        type: string;
        description: string;
        amount: number;
        currency: string;
        status: string;
        date: string;
    }>;
    credits: number;
}

export interface CreditPaymentSummary {
    totalRevenue: number;
    totalRefunded: number;
    successfulPayments: number;
    failedPayments: number;
    totalPayments: number;
    successRate: number;
}

export interface CreditPayment {
    _id: string;
    user: {
        _id: string;
        name: string;
        email: string;
        avatar?: string;
    };
    planName: string;
    amount: number;
    creditsPurchased: number;
    status: string;
    stripePaymentIntentId?: string;
    createdAt: string;
    updatedAt: string;
}

const creditService = {
    // Get all subscription plans
    getAllCreditPlans: async () => {
        const response = await api.get('/credit-plans');
        return response.data;
    },
    getCreditPlans: async () => {
        const response = await api.get<{ success: boolean, plans: SubscriptionPlan[] }>('/credit-plans/available');

        return response.data;
    },



    // Get subscription history with pagination
    getCreditHistory: async (page: number = 1, limit: number = 10): Promise<{
        payments: PaymentHistory[];
        page: number;
        limit: number;
        total: number;
        pages: number;
    }> => {
        const response = await api.get('/credit-plans/history', {
            params: { page, limit }
        });

        return {
            payments: response.data.payments || [],
            page: response.data.page || 1,
            limit: response.data.limit || 10,
            total: response.data.total || 0,
            pages: response.data.pages || 1
        };
    },

    // Get payment history
    getPaymentCreditHistory: async (page: number = 1, limit: number = 10) => {


        const response = await api.get('/admin/credit-payments/history', {
            params: { page, limit }
        });
        return response.data;
    },
    // Get payment history
    getCreditTransactions: async (userId: string, page: number = 1, limit: number = 10) => {


        const response = await api.get(`/admin/credit-payments/history/${userId}`, {
            params: { page, limit }
        });
        return response.data;
    },


    // Get credit payment summary (admin)
    getCreditPaymentSummary: async () => {
        const response = await api.get<{ success: boolean, data: CreditPaymentSummary }>('/admin/credit-payments/summary');
        return response.data;
    },


    // Create a checkout session
    createCheckoutSession: async (priceId: string) => {
        const response = await api.post('/credit-plans/create-checkout-session', { priceId });
        return response.data;
    },

    // Cancel subscription
    cancelSubscription: async (cancelImmediately = false) => {
        const response = await api.post('/credit-plans/cancel', { cancelImmediately });
        return response.data;
    }
};

export default creditService;