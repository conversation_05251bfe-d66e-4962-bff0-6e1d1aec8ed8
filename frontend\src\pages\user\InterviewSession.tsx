import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import UserLayout from '@/components/layout/UserLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Mic, X, Clock, SendHorizontal, Trash2, ChevronDown } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { useAIInterview } from '@/context/AIInterviewContext';

import aiInterviewService from '@/services/aiInterviewService';
import { useUser } from '@clerk/clerk-react';
import { useVoiceToText } from "react-speakup";


// Define interface for conversation entries
interface ConversationEntry {
  question: string;
  answer: string;
  timestamp: string;
}

// Define extended SpeechRecognition interface to include our custom properties
interface ExtendedSpeechRecognition extends SpeechRecognition {
  currentId?: number;
}

const InterviewSession = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { interviewId, scenario, company, position, objective, resumeFile, resumeText } = location?.state || {};
  const { startListening, stopListening, transcript, reset } = useVoiceToText();

  const { user } = useUser();

  const [isRecording, setIsRecording] = useState(false);
  const [conversations, setConversations] = useState<ConversationEntry[]>([]);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [availableMicrophones, setAvailableMicrophones] = useState<MediaDeviceInfo[]>([]);
  const [sessionInfo, setSessionInfo] = useState({
    company: '',
    position: '',
  });


  const timerRef = useRef<NodeJS.Timeout | null>(null);


  const { getInterviewSession, submitInterviewResult, getAIResponse } = useAIInterview();

  // Check if location.state is null and redirect
  useEffect(() => {
    if (!location.state) {
      toast.error('Interview session information is missing');
      navigate('/user/ai-interview');
    }
  }, [location.state, navigate]);

  // Load interview session details
  useEffect(() => {
    const loadSession = async () => {
      try {
        if (interviewId) {
          const session = await getInterviewSession(interviewId);
          setSessionInfo({
            company: session.company,
            position: session.position,
          });
        }
      } catch (error) {
        toast.error('Failed to load interview session');
        navigate('/user/ai-interview');
      }
    };

    // Check browser compatibility
    const checkBrowserCompatibility = () => {
      const hasSpeechRecognition = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;

      if (!hasSpeechRecognition) {
        toast.error('Your browser does not support Speech Recognition. Please use Chrome, Edge, or Firefox.');
        return false;
      }

      return true;
    };

    loadSession();

    // Only proceed if browser is compatible
    if (checkBrowserCompatibility()) {
      // Start the timer when component mounts
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);

      // Load available microphones but don't connect automatically
      checkMicrophonePermission();
    }

    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      stopListening();
      reset();
    };
  }, [navigate, interviewId]);

  // Format elapsed time into hours:minutes:seconds
  const formatTime = (seconds: number) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Check microphone permission
  const checkMicrophonePermission = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const microphones = devices.filter(device => device.kind === 'audioinput');
      setAvailableMicrophones(microphones);

      if (microphones.length === 0) {
        toast.error('No microphones found');
      }
    } catch (error) {
      toast.error('Failed to access microphone devices');
    }
  };




  // Get AI response for the current transcript
  const handleGetResponse = async () => {
    const currentText = transcript;

    if (!currentText.trim()) {
      toast.warning('Please speak something first');
      return;
    }

    try {
      stopListening();

      const finalTranscript = currentText.trim();

      toast.success('Processing your response...');
      toast.loading('Getting AI feedback...', { duration: 10000 });

      try {
        const aiModelsResponse = await aiInterviewService.getAIModels();
        const aiModels = aiModelsResponse.data;

        // Determine which AI to use
        let selectedAI;
        let apiKey;
        let model;

        if (aiModels.openai && aiModels.openai.is_enabled) {
          selectedAI = 'openai';
          apiKey = aiModels.openai.apikey;
          model = aiModels.openai.model;
        } else if (aiModels.gemini && aiModels.gemini.is_enabled) {
          selectedAI = 'gemini';
          apiKey = aiModels.gemini.apikey;
          model = aiModels.gemini.model;
        } else {
          throw new Error('No AI models are enabled');
        }

        const liveInterviewPrompt = `You are acting as the candidate in a live interview simulation. Answer questions naturally, just like a real person would in a professional interview setting.

🎓 Your profile details are as follows:

🔹 Full Name: ${user.fullName}
🔹 Job Role: ${position}
🔹 Company: ${company}
🔹 Scenario: ${scenario}
🔹 Objective: ${objective}
🔹 Resume: ${resumeText}
🔹 Interview Mode: Live Interview
🔹 Time Allotment: 10 minutes
🔹 Feedback Style: Detailed and constructive
 
🎯 Interview Question:
  ${finalTranscript}
  
🧠 Instructions:
- Answer the questions as if you're the real candidate in an interview.
- Be confident, concise, and professional in your tone.
- If it's a technical question, provide code, explanations, or insights.
- For behavioral questions, include examples from your experience.
- Maintain consistency with your profile (skills, experience, etc.).
- Do not respond with "I am an AI" — behave like a human professional.
- Be natural and engaging, just like in a real-time live interview.
- Stay within the interview timeAllotment and keep answers clear and relevant.
- Do not ask questions back to the interviewer unless prompted.`;

        let response;

        if (selectedAI === 'openai') {
          // Call OpenAI API
          const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              model: model,
              messages: [
                { role: 'user', content: liveInterviewPrompt }
              ]
            })
          });

          if (!openAIResponse.ok) {
            throw new Error('OpenAI API request failed');
          }

          const openAIData = await openAIResponse.json();
          response = { answer: openAIData.choices[0].message.content };
        } else if (selectedAI === 'gemini') {
          // Call Gemini API
          const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              contents: [
                {
                  parts: [
                    {
                      text: liveInterviewPrompt
                    }
                  ]
                }
              ]
            })
          });

          if (!geminiResponse.ok) {
            throw new Error('Gemini API request failed');
          }

          const geminiData = await geminiResponse.json();
          response = { answer: geminiData.candidates[0].content.parts[0].text };
        } else {
          // Fallback to our existing API if external APIs fail
          response = await getAIResponse(finalTranscript, interviewId || '');
        }

        toast.dismiss(); // Dismiss any existing toasts

        // Add to conversations
        const newConversation: ConversationEntry = {
          question: finalTranscript,
          answer: response.answer,
          timestamp: new Date().toISOString(),
        };

        setConversations(prev => [newConversation, ...prev]);
        reset();



        // Show success notification
        toast.success('Response received!');



      } catch (error) {
        console.error('Error with AI model selection:', error);

        // Fallback to existing API if model selection fails
        toast.warning('Using fallback AI service...');

        const response = await getAIResponse(finalTranscript, interviewId || '');

        // Add to conversations
        const newConversation: ConversationEntry = {
          question: finalTranscript,
          answer: response.answer,
          timestamp: new Date().toISOString(),
        };

        setConversations(prev => [newConversation, ...prev]);

        toast.success('Response received!');


      }
    } catch (error) {
      console.error('Error getting AI response:', error);
      toast.dismiss(); // Dismiss loading toast
      toast.error('Failed to get AI response. Please try again.');


    }
  };


  // Close session and save data
  const handleCloseSession = async () => {
    try {


      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (conversations.length > 0) {
        await submitInterviewResult(
          interviewId || '',
          conversations,
          {
            duration: elapsedTime,
            endTime: new Date().toISOString()
          }
        );

        toast.success('Interview session saved successfully');
      }

      // Navigate back to interview list
      navigate('/user/ai-interview');
    } catch (error) {
      toast.error('Failed to save interview session');
    }
  };

  // Clean up all resources on unmount
  useEffect(() => {
    return () => {


      if (timerRef.current) {
        clearInterval(timerRef.current);
      }



    };
  }, []);

  return (
    <UserLayout>
      <div className="py-6 relative">
        <div className="border rounded-lg shadow-md overflow-hidden bg-white">
          {/* Header with Logo, Timer and Close Button */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="text-xl font-bold">Interview Session</div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center text-gray-600">
                <Clock className="w-5 h-5 mr-2" />
                <span className="font-mono">{formatTime(elapsedTime)}</span>
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  handleCloseSession();
                  stopListening();
                  reset();
                }}
                className="text-gray-600 hover:text-red-500"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>

          <div className="space-y-4 border-b">
            {/* Session Info */}
            <div className="text-sm text-gray-500 mb-2 p-2">
              <span className="font-medium">{company}</span> - <span>{position}</span>
            </div>

            {/* Microphone Connection UI */}
            <div className="mb-4 flex flex-wrap gap-2 items-center px-2">
              {!isRecording ? (
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => {
                    reset();
                    startListening();
                    setIsRecording(true);
                  }}
                >
                  <Mic className="w-4 h-4" />
                  Connect Microphone
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  {isRecording && (
                    <div className="flex items-center gap-2 text-green-600 px-3 py-1 bg-green-50 border border-green-200 rounded-md">
                      <div className="animate-pulse w-2 h-2 rounded-full bg-green-600"></div>
                      <span className="text-sm">Recording...</span>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    className="flex items-center gap-2 text-red-600 border-red-200 bg-red-50 hover:bg-red-100"
                    onClick={() => {
                      stopListening();
                      reset();
                      setIsRecording(false);
                    }}
                  >
                    <X className="w-4 h-4" />
                    Disconnect Microphone
                  </Button>
                </div>
              )}
            </div>

            {/* Live Transcription */}

            <CardContent className="pt-6 border-t">
              <div className="flex flex-col space-y-4">
                <div className="text-sm font-medium text-gray-500">Live Transcription</div>
                <textarea
                  className="min-h-[300px] p-2 bg-gray-50 rounded-md border resize-none w-full"
                  value={transcript}
                  readOnly
                  placeholder="Speak into the microphone to see live transcription here..."
                />
              </div>
            </CardContent>

            {/* Action Buttons */}
            <div className="flex space-x-2 mb-6 pb-2 px-2">
              <Button
                className="flex-grow bg-blue-600 hover:bg-blue-700 text-white font-medium"
                onClick={handleGetResponse}
                disabled={!transcript.trim()}
              >
                <SendHorizontal className="w-4 h-4 mr-2" />
                Finish & Get Response
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  reset();

                }}
                className="border-gray-300"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Conversation History */}
            {conversations.length > 0 && (
              <Card className="border">
                <CardContent className="pt-6">
                  <div className="text-sm font-medium text-gray-500 mb-4">Conversation History</div>
                  <ScrollArea className="h-[300px] pr-4">
                    <div className="space-y-4">
                      {conversations.map((conv, index) => (
                        <div key={index} className="border-b pb-4 last:border-0">
                          <div className="font-medium">Question:</div>
                          <div className="mb-2 text-gray-700 bg-gray-50 p-3 rounded-md border">
                            {conv.question}
                          </div>
                          <div className="font-medium">Answer:</div>
                          <div className="text-gray-700 bg-gray-50 p-3 rounded-md border">
                            {conv.answer}
                          </div>
                          <div className="text-xs text-gray-500 mt-2">
                            {new Date(conv.timestamp).toLocaleString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </UserLayout>
  );
};

export default InterviewSession;  