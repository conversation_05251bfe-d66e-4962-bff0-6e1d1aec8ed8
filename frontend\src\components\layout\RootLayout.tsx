import React, { useEffect, useState } from 'react';
import { settingsService } from '@/services/settingsService';
import type { SiteSettings } from '@/services/settingsService';
import { getImageUrl } from '@/utils/imageUtils';

interface RootLayoutProps {
  children: React.ReactNode;
}

const RootLayout: React.FC<RootLayoutProps> = ({ children }) => {
  const [settings, setSettings] = useState<SiteSettings | null>(null);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const siteSettings = await settingsService.getSiteSettings();
        setSettings(siteSettings);

        // Set document title
        document.title = siteSettings.site_title;

        // Inject Google Analytics if provided
        if (siteSettings.google_analytics_code) {
          const gaScript = document.createElement('script');
          gaScript.innerHTML = siteSettings.google_analytics_code;
          document.head.appendChild(gaScript);
        }

        // Inject custom code if provided
        if (siteSettings.custom_code) {
          const customScript = document.createElement('script');
          customScript.innerHTML = siteSettings.custom_code;
          document.body.appendChild(customScript);
        }
      } catch (error) {
        console.error('Error loading site settings:', error);
      }
    };

    // loadSettings();

    // Cleanup function to remove injected scripts
    return () => {
      const scripts = document.querySelectorAll('script[data-custom="true"]');
      scripts.forEach(script => script.remove());
    };
  }, []);

  return (
    <>
      {children}
    </>
  );
};

export default RootLayout;