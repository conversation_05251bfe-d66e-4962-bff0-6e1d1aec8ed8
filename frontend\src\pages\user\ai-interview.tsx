import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format, formatDistanceToNow } from 'date-fns';

import { Upload, RefreshCw, Trash2, PlusCircle, Circle, CheckCircle2, FileText, X } from 'lucide-react';
import { useAIInterview } from '@/context/AIInterviewContext';
import UserLayout from '@/components/layout/UserLayout';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import aiInterviewService, { ConversationEntry, InterviewSummary } from '@/services/aiInterviewService';
import { parseChatGPTResponse } from '@/utils/chatgptParser';
import { useAuth } from '@/context/AuthContext';
import IntervieweeSummaryDialog from '@/components/ai-interview/IntervieweeSummaryDialog';
 
// Define scenario data
const scenarioData: Record<string, { name: string; description: string }> = {
  'GENERAL_PURPOSE': {
    name: 'General Purpose',
    description: 'Ideal for general, behavioral and experience-based interviews.'
  },
  'SOFTWARE_ENGINEERING': {
    name: 'Software Engineering',
    description: 'Ideal for software engineering interviews and coding challenges. Perfect for practicing coding problems and getting feedback on your solutions. Great for preparing for technical meetings and coding assessments. Cost-effective choice for coding.'
  },
  'SYSTEM_DESIGN': {
    name: 'System Design',
    description: 'Perfect for system design discussions and architecture reviews. Ideal for practicing system design interviews, getting feedback on your solutions, and refining system architectures. Great for preparing for system design challenges and large-scale project planning.'
  },
  'PROJECT_MANAGER': {
    name: 'Project Manager',
    description: 'Perfect for project management interviews and sprint planning. Ideal for discussing project timelines, resource allocation, and coordinating teams. Great for improving project delivery and leadership skills.'
  },
  'CONSULTANT': {
    name: 'Consultant',
    description: 'Suitable for consulting interviews and case study practice. Perfect for practicing case studies and getting feedback on your solutions. Essential for preparing for consulting meetings and case study assessments. Ideal for case study.'
  },
  'CYBER_SECURITY': {
    name: 'Cyber Security',
    description: 'Designed for cyber security interviews and security scenarios. Perfect for practicing security problems and getting feedback on your solutions. Great for preparing for security meetings and security assessments. Excellent choice for security.'
  },
  'DATA_SCIENCE': {
    name: 'Data Science',
    description: 'Perfect for data science interviews and data analysis. Ideal for practicing data problems and getting feedback on your solutions. Great for preparing for data meetings and data assessments. Essential for data analysis.'
  },
  'CLOUD_ENGINEERING': {
    name: 'Cloud Engineering',
    description: 'Suitable for cloud engineering interviews and cloud scenarios. Perfect for practicing cloud problems and getting feedback on your solutions. Essential for preparing for cloud meetings and cloud assessments. Ideal for cloud scenarios.'
  },
  'DEVOPS': {
    name: 'DevOps',
    description: 'Designed for DevOps interviews and DevOps scenarios. Perfect for practicing DevOps problems and getting feedback on your solutions. Great for preparing for DevOps meetings and DevOps assessments. Excellent choice for DevOps.'
  },
  'MACHINE_LEARNING': {
    name: 'Machine Learning',
    description: 'Perfect for machine learning interviews and machine learning scenarios. Ideal for practicing machine learning problems and getting feedback on your solutions. Great for preparing for machine learning meetings and machine learning assessments. Essential for machine learning.'
  },
  'SALES': {
    name: 'Sales',
    description: 'Designed for sales interviews and client engagement. Perfect for practicing sales pitches and refining your communication strategy. Great for preparing for sales meetings and closing deals. Excellent choice for enhancing sales performance.'
  },
  'MARKETING': {
    name: 'Marketing',
    description: 'Perfect for marketing interviews and campaign brainstorming. Ideal for preparing marketing strategies, discussing target audiences, and refining campaign messages. Great for improving communication within marketing teams.'
  },
  'FINANCE': {
    name: 'Finance',
    description: 'Ideal for finance interviews and budget planning. Great for discussing financial strategies, reviewing reports, and preparing for client or internal meetings. Essential for financial analysts and managers to streamline their financial planning.'
  },
  'LEGAL_ADVICE': {
    name: 'Legal Advice (Not real lawyer)',
    description: 'Suitable for legal interviews and case discussions. Ideal for preparing legal arguments, reviewing contracts, and discussing legal strategies. Perfect for lawyers looking to improve client communication and legal negotiation skills.'
  },
  'UX_UI_DESIGN': {
    name: 'UX/UI Design',
    description: 'Ideal for UX/UI design interviews and collaboration. Great for discussing design processes, prototyping, and user flow improvements. Essential for designers aiming to refine their user-centric strategies and deliver impactful experiences.'
  },
  'HR_MANAGEMENT': {
    name: 'HR Management',
    description: 'Ideal for HR interviews and workforce management. Great for discussing recruitment strategies, performance reviews, and employee engagement. Essential for HR professionals aiming to streamline processes and foster a positive workplace culture.'
  },
  'CUSTOMER_SERVICE': {
    name: 'Customer Service',
    description: 'Ideal for customer service interviews and support scenarios. Great for practicing client interactions, handling complaints, and improving communication. Essential for support representatives looking to enhance their customer relationship skills.'
  },
  'BUSINESS_ANALYST': {
    name: 'Business Analyst',
    description: 'Ideal for business analyst interviews and stakeholder discussions. Great for reviewing requirements, analyzing business processes, and identifying improvement opportunities. Essential for business analysts aiming to sharpen their data-driven insights and communication skills.'
  },
  'QA_TESTING': {
    name: 'QA Testing',
    description: 'Ideal for QA testing, interviews and ensuring software quality. Great for designing test cases, identifying bugs, and overseeing the QA process. Essential for QA professionals looking to refine their strategies for defect prevention and process improvement. '
  },
  'GAME_DEVELOPMENT': {
    name: 'Game Development',
    description: 'Ideal for game development interviews and design reviews. Great for discussing mechanics, storylines, and user engagement strategies. Essential for game developers aiming to refine their design approach and produce more immersive gaming experiences.'
  }
};

// Define form schema with Zod
const formSchema = z.object({
  scenario: z.string().min(1, 'Scenario is required'),
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  objective: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const AIInterviewPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const { interviews, isLoading, isSubmitting, fetchInterviews, createInterview, deleteInterview } = useAIInterview();
  const [showForm, setShowForm] = useState(false);
  const [userInterviews, setUserInterviews] = useState<InterviewSummary[]>([]);
  const [selectedInterview, setSelectedInterview] = useState<InterviewSummary | null>(null);
  const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);
  const [pdfText, setPdfText] = useState('');

   
  // Create a custom function to handle PDF extraction that doesn't rely on worker
  const extractTextFromPDF = async (file: File) => {
    try {
      // Read the file as ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Simple text extraction from PDF binary data
      let text = '';
      for (let i = 0; i < uint8Array.length; i++) {
        // Only extract readable ASCII characters
        if (uint8Array[i] > 31 && uint8Array[i] < 127) {
          text += String.fromCharCode(uint8Array[i]);
        }
      }
      
      // Basic cleaning of the extracted text
      const cleanedText = text
        .replace(/[^\x20-\x7E\n]/g, '')  // Remove non-printable chars
        .replace(/\s+/g, ' ')            // Normalize whitespace
        .trim();
      
      // Format the extracted text as a JSON-friendly string
      // This will help when parsing the response from ChatGPT
      const jsonReadyText = JSON.stringify({
        resumeText: cleanedText || 'No readable text found in the PDF.',
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        extractionTime: new Date().toISOString()
      });
       
      console.log('jsonReadyText----', jsonReadyText);
      // Set the extracted text in state
      setPdfText(jsonReadyText);
      
      // Test the parser with a sample ChatGPT response
      if (process.env.NODE_ENV === 'development') {
        testParser();
      }
      
      console.log('PDF text extracted as JSON-ready format');
      toast.success('Resume text extracted successfully');
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      toast.error('Failed to extract text from PDF');
      setPdfText('');
    }
  };
  
  // Test function to demonstrate parser functionality
  const testParser = () => {
    const sampleResponse = `
✅ Overall Impression & Visual Appeal
Strengths:
• Clean, modern, and professional layout.
• Good use of whitespace and icons for readability.
• Visual hierarchy (bold headers, bullet points) is clear.

Areas to Improve:
• Your photo looks professional, but some companies (especially in the US) advise against including a headshot due to bias concerns and ATS compatibility. Consider removing it for US-based roles.

✅ Structure & Organization
Strengths:
• Logically organized: Summary → Experience → Education → Skills → Certifications → Passions.
• Consistent formatting across sections.

Areas to Improve:
• The "Passions" section is a nice personal touch, but might be better as an optional addition (e.g., "Volunteer Work" or "Interests") unless it directly supports your technical or mentoring strengths.

✅ Final Rating
Visual Appeal: 8.5/10
Content Quality: 8/10
ATS Compatibility: 6.5/10
Job-Readiness: 8/10

With just a few tweaks—especially removing the photo, expanding your skills, and adding a project section—you'll have a resume that's both recruiter-friendly and ATS-compliant.
    `;
    
    // Parse the sample response
    const parsedResult = parseChatGPTResponse(sampleResponse);
    console.log('Parsed ChatGPT response:', parsedResult);
    
    // Show the result as a toast notification
    toast.info('Parsed sample response into JSON', {
      description: 'Check console for details'
    });
  };
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      scenario: 'SOFTWARE_ENGINEERING',
      company: '',
      position: '',
      objective: '',
    },
  });

  // Load interviews on page load
  useEffect(() => {
    fetchInterviews();
    fetchUserInterviews();
  }, []);

   // Fetch user interviews
  const fetchUserInterviews = async () => {
    try {
      console.log('user');
      const response = await  aiInterviewService.getUserInterviews(user?.userId);
      // const response = await useAIInterview().aiInterviewService.getUserInterviews(user?.userId);
      console.log('response',response);
      setUserInterviews(response.data);
    } catch (error) {
      console.error('Failed to fetch user interviews:', error);
      toast.error('Failed to load your interview sessions');
    }
  };

  const onSubmit = async (values: FormValues) => {
    try {
      let data: {
        scenario: string;
        company: string;
        position: string;
        interviewId: any;
        objective?: string;
        resumeFile?: File;
        resumeText?: string;
      } = {
        scenario: values.scenario,
        company: values.company,
        position: values.position,
        interviewId: null,
      };

      const formData = new FormData();
      formData.append('scenario', values.scenario);
      formData.append('company', values.company);
      formData.append('position', values.position);

      if (values.objective) {
        formData.append('objective', values.objective);
        data.objective = values.objective;
      }

      if (resumeFile) {
        formData.append('resume', resumeFile);
        data.resumeFile = resumeFile;
        
        // Create a system prompt that instructs ChatGPT to return JSON
        const systemPrompt = `
When analyzing this resume, please return ONLY a valid JSON object with the following structure:
{
  "overallScore": number, // Overall score out of 100
  "summary": string, // Brief overall assessment
  "sections": [
    {
      "name": string, // Section name like "Professional Summary", "Work Experience", etc.
      "score": number, // Section score out of 100
      "recommendations": string[] // Array of recommendation strings
    }
  ]
}

No other text, explanations, or markdown formatting should be included in your response.
Just return the raw JSON object that can be parsed with JSON.parse().
`;

        // Add resume text and instructions
        let resumeData;
        try {
          // Try to parse existing pdfText if it's already JSON
          resumeData = JSON.parse(pdfText);
        } catch (e) {
          // If not JSON, use it as plain text
          resumeData = { resumeText: pdfText };
        }
        
        // Complete structured prompt
        const structuredPrompt = {
          systemPrompt,
          resumeData,
          returnFormat: "JSON",
          instructions: "Analyze this resume and provide feedback on its effectiveness, organization, and content."
        };
        
        // Stringify for transmission
        data.resumeText = JSON.stringify(structuredPrompt);
        formData.append('resumeText', data.resumeText);
      }

      const interview = await createInterview(formData);
      data.interviewId = interview._id;
      form.reset();
      setResumeFile(null);
      setShowForm(false);
      navigate('/user/interview-session', {
        state: data,
      });

      // Refresh the interview list after creating a new one
      fetchUserInterviews();
    } catch (error) {
      // Error is already handled in the context
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }
    
    // Check file type
    if (!file.type.includes('pdf')) {
      toast.error('Please upload a PDF file');
      return;
    }
    
    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size exceeds 5MB limit');
      return;
    }
    
    setResumeFile(file);
    toast.info('Extracting text from PDF...');
    await extractTextFromPDF(file);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteInterview(id);
      // Refresh the interview list after deletion
      fetchUserInterviews();
    } catch (error) {
      // Error is already handled in the context
    }
  };

  const resetForm = () => {
    form.reset();
    setResumeFile(null);
  };

  // Helper function to get scenario display text
  const getScenarioText = (scenarioCode: string) => {
    return scenarioData[scenarioCode]?.name ||
      scenarioCode.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Open summary dialog
  const openSummaryDialog = async (interview: InterviewSummary) => {
    setSelectedInterview(interview);
    setSummaryDialogOpen(true);
  };

  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins} mins ${secs} secs`;
  }
  return (
    <UserLayout>
      <div className="container max-w-7xl mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">AI Live Interview</h1>
            <p className="text-sm text-gray-500">Configure AI-powered interview scenarios</p>
          </div>
        </div>


        <Card className="mb-8 border border-gray-200 shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-xl">New Interview Configuration</CardTitle>
            <CardDescription>
              Set up a new AI-powered interview scenario
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="scenario"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Interview Scenario</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a scenario" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-white border border-gray-200">
                          {Object.entries(scenarioData).map(([key, value]) => (
                            <SelectItem key={key} value={key}>
                              {value.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {scenarioData[form.watch('scenario')]?.description || 'Choose a scenario for your interview.'}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Company Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter company name"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        The name of the company you're interviewing for.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Position</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter position title"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        The position or role you're applying for.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="objective"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Interview Objective (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your interview objective"
                          className="resize-none"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        Your specific goals or focus areas for this interview (optional).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>Resume (Optional)</FormLabel>
                  <div className="flex items-center gap-2">
                    <Input
                      type="file"
                      onChange={handleFileChange}
                      className="hidden"
                      id="resume-upload"
                      accept=".pdf,.doc,.docx"
                      disabled={isSubmitting}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="flex gap-2"
                      onClick={() => document.getElementById('resume-upload')?.click()}
                      disabled={isSubmitting}
                    >
                      <Upload className="h-4 w-4" />
                      Upload Resume
                    </Button>
                    {resumeFile && (
                      <span className="text-sm text-gray-600">
                        {resumeFile.name}
                      </span>
                    )}
                  </div>
                  <FormDescription>
                    Upload your resume to tailor the interview questions (PDF, DOC, DOCX, max 5MB).
                  </FormDescription>
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  {form.formState.isDirty && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={resetForm}
                      disabled={isSubmitting}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Reset
                    </Button>
                  )}
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating Interview...
                      </>
                    ) : (
                      <>
                        Start Interview
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        
        
        {/* Interview History Table */}
        <Card className="border border-gray-200 shadow-sm mt-8">
          <CardHeader className="pb-3">
            <CardTitle className="text-xl">Your Interview Sessions</CardTitle>
            <CardDescription>
              View and manage your AI interview sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : userInterviews.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No interview sessions found. Start a new interview to see it here.</p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Company Name</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Ends In</TableHead>
                      <TableHead>Created At</TableHead>
                      <TableHead className="text-right">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userInterviews.map((interview) => (
                      <TableRow key={interview._id}>
                        <TableCell className="font-medium">{interview.company}</TableCell>
                        <TableCell>{interview.position}</TableCell>
                        <TableCell>{formatTime(interview.duration)}</TableCell>
                        <TableCell>{format(new Date(interview.createdAt), 'MMM dd, yyyy HH:mm')}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openSummaryDialog(interview)}
                            className="inline-flex items-center gap-1"
                          >
                            <FileText className="h-4 w-4" />
                            Summary
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col"> */}
        {/* Summary Dialog */}
        {/* <Dialog open={summaryDialogOpen} onOpenChange={setSummaryDialogOpen}>
            <DialogHeader>
              <DialogTitle>Interview Summary</DialogTitle>
              <DialogDescription>
                {selectedInterview && (
                  <div>
                    <p><strong>Company:</strong> {selectedInterview.company}</p>
                    <p><strong>Position:</strong> {selectedInterview.position}</p>
                    <p><strong>Created:</strong> {format(new Date(selectedInterview.createdAt), 'MMM dd, yyyy HH:mm')}</p>
                  </div>
                )}
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="flex-1 mt-4 max-h-[500px] pr-4">
              {selectedInterview && selectedInterview.conversations && selectedInterview.conversations.length > 0 ? (
                <div className="space-y-6">
                  {selectedInterview.conversations.map((conversation, index) => (
                    <div key={index} className="space-y-3">
                      <div className="bg-gray-100 p-3 rounded-lg">
                        <p className="font-semibold text-gray-900">Question:</p>
                        <p className="mt-1">{conversation.question}</p>
                      </div>
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <p className="font-semibold text-primary">Answer:</p>
                        <p className="mt-1">{conversation.answer}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No conversation data available for this interview.</p>
                </div>
              )}
            </ScrollArea>
            <div className="flex justify-end mt-4">
              <DialogClose asChild>
                <Button variant="outline">Close</Button>
              </DialogClose>
            </div>
          </DialogContent>
        </Dialog> */}
         {/* Interviewee Summary Dialog */}
        <IntervieweeSummaryDialog
          open={summaryDialogOpen}
          onOpenChange={setSummaryDialogOpen}
          summary={selectedInterview}
        />
      </div>
    </UserLayout>
  );
};

export default AIInterviewPage; 