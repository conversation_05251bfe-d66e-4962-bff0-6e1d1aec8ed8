import jwt from 'jsonwebtoken';
import Admin from '../models/adminModel.js';
import User from '../models/userModel.js';

// Protect admin routes
export const protectAdminRoutes = async (req, res, next) => {
  try {
    let token;

    // Check if token exists in headers
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    // If no token in headers, check for token in cookies
    if (!token && req.cookies && req.cookies.auth_token) {
      token = req.cookies.auth_token;
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }
    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      // Find admin by id
      const admin = await Admin.findById(decoded.id);

      // Check if admin exists
      if (!admin) {
        return res.status(401).json({
          success: false,
          message: 'Not authorized to access this route',
        });
      }

      // Set admin in request
      req.admin = {
        id: admin._id,
        email: admin.email,
      };

      next();
    } catch (error) {

      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message,
    });
  }
};

// Protect user routes
export const protectUserRoutes = async (req, res, next) => {
  try {
    let token;

    // Check if token exists in headers
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    // If no token in headers, check for token in cookies
    if (!token && req.cookies && req.cookies.auth_token) {
      token = req.cookies.auth_token;
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('decoded-----', decoded);
      // Find user by id
      let user;

      // Check if this is a clerkId (starts with "user_")
      if (decoded.id.startsWith('user_')) {
        user = await User.findOne({ clerkId: decoded.id });
      } else {
        user = await User.findById(decoded.id);
      }
      console.log('user-----', user);
      if (user) {
        console.log('User details:', {
          id: user._id,
          email: user.email,
          clerkId: user.clerkId
        });
      }

      // Check if user exists
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Not authorized to access this route',
        });
      }

      // Set user in request with _id property (not just id)
      req.user = {
        _id: user._id,  // Use _id to match MongoDB document's _id
        id: user._id,   // Keep id for backward compatibility
        email: user.email,
        clerkId: user.clerkId,
        role: user.role,
      };

      next();
    } catch (error) {
      console.error('Token verification error:', error);
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message,
    });
  }
};

// Admin only middleware
export const adminOnly = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Access denied. Admin only.',
    });
  }
};

// Protect routes for both users and admins
// This middleware allows either users or admins to access routes
// by checking for both types of authentication tokens.
// It provides a flexible authorization method for shared resources
// like AI Interview features that both regular users and administrators need to access.
export const protectSharedRoutes = async (req, res, next) => {
  try {
    let token;

    // Check if token exists in headers
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    // If no token in headers, check for token in cookies
    if (!token && req.cookies && req.cookies.auth_token) {
      token = req.cookies.auth_token;
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      // First try to find an admin
      const admin = await Admin.findById(decoded.id);
      if (admin) {
        // Set admin in request
        req.admin = {
          id: admin._id,
          email: admin.email,
        };
        return next();
      }

      // If not an admin, try to find a user
      let user;

      // Check if this is a clerkId (starts with "user_")
      if (decoded.id.startsWith('user_')) {
        user = await User.findOne({ clerkId: decoded.id });
      } else {
        user = await User.findById(decoded.id);
      }
      // Check if user exists
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Not authorized to access this route',
        });
      }

      // Set user in request with _id property (not just id)
      req.user = {
        _id: user._id,
        id: user._id,
        email: user.email,
        clerkId: user.clerkId,
        role: user.role,
      };

      next();
    } catch (error) {
      console.error('Token verification error:', error);
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message,
    });
  }
};
