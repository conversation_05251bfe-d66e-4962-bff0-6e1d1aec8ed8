import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useUsers } from "@/context/UserContext";
import { userApi } from "@/services/api";
import DashboardLayout from "@/components/layout/DashboardLayout";
import StatusBadge from "@/components/common/StatusBadge";
import ConfirmDialog from "@/components/common/ConfirmDialog";
import { useToast } from "@/components/ui/use-toast";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, MoreHorizontal, Edit, Trash2, User, Shield, Package, CreditCard, Info, Loader2, ExternalLink } from "lucide-react";
import creditService from "@/services/creditService";
import subscriptionService from "@/services/subscriptionService";

// Define interface for user data
interface UserData {
  id: string;
  _id?: string;
  name: string;
  email: string;
  avatar: string;
  status: string;
  role: string;
  createdAt: string;
  lastLogin?: string;
  subscription?: {
    status: string;
    planId?: {
      name?:string
    };
    planName?: string;
    startDate?: string;
    endDate?: string;
    currentPeriodStart?: string;
    currentPeriodEnd?: string;
    stripeSubscriptionId?: string;
    stripeCustomerId?: string;
    autoRenew?: boolean;
    cancelAtPeriodEnd?: boolean;
    availableCredits?: number;
  };
  subscriptionHostory?: any[];
  paymentHistory?: any[];
  creditTransactions?: any[];
  credits?: number;
  clerkId?: string;
  creditsPurchased: number,
  subscriptionCredits:number
}

const UserDetail: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { getUserSubscriptions, getUserPayments, deleteUser, updateUser } = useUsers();

  const [user, setUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<any[]>([]);
   const [creditTransactions, setCreditTransactions] = useState<any[]>([]);

  // Fetch user data from API
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId) return;

      try {
        setIsLoading(true);
        setError(null);

        const response = await userApi.getUserById(userId);
        const creditResponse = await creditService.getCreditTransactions(userId, 1, 10);
        console.log('creditResponse', creditResponse);
        const subscriptionResponse = await subscriptionService.getUserSubscriptionPaymentHistory(userId, 1, 10);
        console.log('subscriptionResponse', subscriptionResponse);

        if (subscriptionResponse.success) {
          setSubscriptionHistory(subscriptionResponse.payments);
        }

        if (creditResponse.success) {
          setCreditTransactions(creditResponse.data);
        }



         if (response.success && response.data) {
          // Format user data
          const userData: UserData = {
            id: response.data._id || response.data.id,
            ...response.data,
            // Ensure id is available for consistency
            _id: response.data._id || response.data.id
          };

          setUser(userData);
        } else {
          setError("Failed to load user data");
        }
      } catch (err) {
        console.error("Error fetching user:", err);
        setError("Error loading user data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load user data.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId, toast]);

  // Fallback to context data for subscriptions and payments
  const userSubscriptions = getUserSubscriptions(userId || "");
  const userPayments = getUserPayments(userId || "");

  const handleDeleteUser = async () => {
    if (!userId) return;

    try {
      await deleteUser(userId);
      toast({
        title: "Success",
        description: "User has been deleted successfully"
      });
      navigate("/admin/users");
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive"
      });
    }
  };

  const handleUpdateStatus = async (newStatus: "active" | "inactive" | "suspended"): Promise<void> => {
    try {
      setIsLoading(true);
      await updateUser(userId, { status: newStatus });
      setUser(prev => prev ? { ...prev, status: newStatus } : null);
      toast({
        title: "Status updated",
        description: `User status has been updated to ${newStatus}`,
      });
    } catch (error) {
      console.error("Error updating user status:", error);
      toast({
        title: "Error",
        description: "Failed to update user status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !user) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-xl font-semibold mb-2">User not found</h1>
          <p className="text-gray-500 mb-6">{error || "The user you're looking for doesn't exist or has been deleted."}</p>
          <Button onClick={() => navigate("/admin/users")}>Back to Users</Button>
        </div>
      </DashboardLayout>
    );
  }

  const formatDate = (dateString?: string | Date) => {
    if (!dateString) return "N/A";

    try {
      return new Date(dateString).toLocaleString();
    } catch (error) {
      return "Invalid date";
    }
  };

  // Replace formatStatus function with a properly typed version
  const formatStatus = (status: string): "active" | "inactive" | "suspended" | "expired" | "canceled" | "pending" | "successful" | "failed" | "refunded" | "open" | "low" | "in-progress" | "medium" | "closed" | "resolved" | "high" | "success" => {
    return status as "active" | "inactive" | "suspended" | "expired" | "canceled" | "pending" | "successful" | "failed" | "refunded" | "open" | "low" | "in-progress" | "medium" | "closed" | "resolved" | "high" | "success";
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6 mx-auto py-10">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={() => navigate("/admin/users")} className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            Back to Users
          </Button>
          <DropdownMenu >
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="h-4 w-4 mr-2" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border border-gray-200">
              <DropdownMenuLabel>User Actions</DropdownMenuLabel>
              {/* <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </DropdownMenuItem> */}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleUpdateStatus("active")}>
                Set as Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleUpdateStatus("inactive")}>
                Set as Inactive
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleUpdateStatus("suspended")}>
                Suspend User
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600" onClick={() => setIsDeleteDialogOpen(true)}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete User
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex gap-4 items-start">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-lg">{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{user.name}</h2>
                <p className="text-slate-500">{user.email}</p>
                <div className="flex gap-2 mt-2">
                  {user.clerkId && (
                    <Badge variant="outline" className="cursor-pointer hover:underline">
                      Clerk ID: {user.clerkId}
                    </Badge>
                  )}
                </div>
              </div>
            </div>



            <div className="mt-4 sm:mt-0 flex flex-col sm:items-end gap-1">
              <div className="flex gap-4 mb-1">

                <h4 className="text-sm font-medium text-gray-500">User Status:</h4>
                <StatusBadge status={formatStatus(user.status)} size="md" />
              </div>
              {user.clerkId && (
                <Badge variant="outline">
                  User ID: {user.id}
                </Badge>
              )}


            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="font-semibold text-gray-500 mb-1">Date Joined</h3>
                <p>{formatDate(user.createdAt)}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-500 mb-1">Last Login</h3>
                <p>{user.lastLogin ? formatDate(user.lastLogin) : "Never"}</p>
              </div>

            </div>
            <div className="space-y-4">

              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    Account Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      onClick={() => handleUpdateStatus("active")}
                      size="sm"
                      variant={formatStatus(user.status) === "active" ? "default" : "outline"}
                    >
                      Activate
                    </Button>
                    <Button
                      onClick={() => handleUpdateStatus("inactive")}
                      size="sm"
                      variant={formatStatus(user.status) === "inactive" ? "default" : "outline"}
                    >
                      Deactivate
                    </Button>
                    <Button
                      onClick={() => handleUpdateStatus("suspended")}
                      size="sm"
                      variant={formatStatus(user.status) === "suspended" ? "default" : "outline"}
                    >
                      Suspend
                    </Button>
                    <Button
                      onClick={() => setIsDeleteDialogOpen(true)}
                      size="sm"
                      variant="destructive"
                    >
                      Delete Account
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
            {user.subscription && (
              <div className="mt-6 p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Package className="h-5 w-5 mr-2 text-blue-600" />
                  Current Subscription
                  <div className="p-1">
                    <StatusBadge status={formatStatus(user.subscription.status)} />
                  </div>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Plan</h4>
                    <p className="font-medium">{user.subscription.planId?.name || "No Plan"}</p>
                  </div>
                  {/* <div>
                      <h4 className="text-sm font-medium text-gray-500">Status</h4>
                      <StatusBadge status={formatStatus(user.subscription.status)} />
                    </div> */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Subscription Credits</h4>
                    <p className="font-medium">{user.subscriptionCredits || 0}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Purchase Credits</h4>
                    <p className="font-medium">{user.creditsPurchased || 0}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Renewal Date</h4>
                      <p>{user.subscription?.endDate ? formatDate(user.subscription.endDate) : "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Start Date</h4>
                      <p>{user.subscription?.startDate ? formatDate(user.subscription.startDate) : "N/A"}</p>
                    </div>
                  </div>

                  {user.subscription?.stripeCustomerId && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-500">Customer ID</h4>
                      <div className="flex items-center gap-1">
                        <code className="text-sm bg-slate-100 p-1 rounded">{user.subscription.stripeCustomerId}</code>
                        <a
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center hover:text-primary"
                          href={`https://dashboard.stripe.com/test/customers/${user.subscription?.stripeCustomerId}`}>
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </div>
                    </div>
                  )}
                  {user.subscription?.stripeSubscriptionId && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-500">Stripe Subscription ID</h4>
                      <div className="flex items-center gap-1">
                        <code className="text-sm bg-slate-100 p-1 rounded">{user.subscription.stripeSubscriptionId}</code>
                        <a
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center hover:text-primary"
                          href={`https://dashboard.stripe.com/test/subscriptions/${user.subscription?.stripeSubscriptionId}`}>
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <Separator />

            <Tabs defaultValue="subscriptions" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="subscriptions">Subscription History</TabsTrigger>
                 <TabsTrigger value="credit-payments">Credit Payments</TabsTrigger>
              </TabsList>

              <TabsContent value="subscriptions" className="mt-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Plan Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Plan Status</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>Renewal/End Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Credits</TableHead>
                        <TableHead>Invoice ID</TableHead>
                        <TableHead>Payment ID</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subscriptionHistory.length > 0 ? (
                        subscriptionHistory.map((subscription) => (
                          <TableRow key={subscription.id}>
                            <TableCell>{formatDate(subscription.transactionDate)}</TableCell>
                            <TableCell className="font-medium">{subscription.planName}</TableCell>
                            <TableCell> <StatusBadge status={formatStatus(subscription.subscriptionStatus)} />  </TableCell>
                            <TableCell>{subscription.eventType}</TableCell>
                            <TableCell>{formatDate(subscription.startDate)}</TableCell>
                            <TableCell>{subscription.autoRenew === true ? 'Yes' : formatDate(subscription.endDate)} </TableCell>
                            <TableCell>${subscription.amount || '0.00'}/{subscription.billingInterval}</TableCell>
                            <TableCell>{subscription.subscriptionCredits}</TableCell>
                            <TableCell>
                              {subscription.invoiceId ? <a
                                href={`https://dashboard.stripe.com/test/invoices/${subscription?.invoiceId}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center hover:text-primary"
                              >
                                {subscription.invoiceId?.substring(0, 10)}...
                                <ExternalLink className="ml-1 h-3 w-3" />
                              </a>
                              : 'N/A'}
                            </TableCell>
                            <TableCell>
                              {subscription.stripePaymentId ? <a
                                href={`https://dashboard.stripe.com/test/payments/${subscription?.stripePaymentId}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center hover:text-primary"
                              >
                                {subscription.stripePaymentId?.substring(0, 10)}...
                                <ExternalLink className="ml-1 h-3 w-3" />
                              </a>
                              : 'N/A'}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            No subscription history found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              
              <TabsContent value="credit-payments" className="mt-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Transaction Date</TableHead>
                        <TableHead>Plan Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Credits</TableHead>
                        <TableHead>Payment ID</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {creditTransactions?.length > 0 ? (
                        creditTransactions?.map((credit) => (
                          <TableRow key={credit._id}>
                            <TableCell>{formatDate(credit.createdAt)}</TableCell>
                            <TableCell className="font-medium">{credit.planName}</TableCell>
                            <TableCell>
                              <StatusBadge status={formatStatus(credit.status)} />
                            </TableCell>
                            <TableCell>{formatCurrency(credit.amount || 0)}</TableCell>
                            <TableCell>{credit.creditsPurchased}</TableCell>

                            <TableCell className="font-mono text-xs">
                              <a
                                href={`https://dashboard.stripe.com/test/payments/${credit.stripePaymentIntentId}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center hover:text-primary"
                              >
                                {credit.stripePaymentIntentId?.substring(0, 10)}...
                                <ExternalLink className="ml-1 h-3 w-3" />
                              </a>

                            </TableCell>

                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4">
                            No subscription history found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <ConfirmDialog
        open={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteUser}
        title="Delete User"
        description="Are you sure you want to delete this user? This action cannot be undone and will remove all their data, including subscription and payment history."
        confirmLabel="Delete User"
        variant="destructive"
      />
    </DashboardLayout>
  );
};

export default UserDetail;
