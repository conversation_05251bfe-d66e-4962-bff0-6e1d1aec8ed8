import React, { useState, useRef, useEffect } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { useAuth } from "@/context/AuthContext";
import { settingsService, SiteSettings } from "@/services/settingsService";
import { toast } from "@/components/ui/use-toast";
import { getImageUrl } from "@/utils/imageUtils";
import { useForm } from 'react-hook-form';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Save, User, Shield, Globe, Upload, Loader2 } from "lucide-react";

interface SettingsFormData {
  logo_url: string;
  google_analytics_code: string;
  custom_code: string;
}

interface ProfileFormData {
  name: string;
  email: string;
  avatar: string;
}

interface PasswordFormData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const Settings: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);
  
  // State for form values
  const [siteName, setSiteName] = useState("");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [googleAnalyticsCode, setGoogleAnalyticsCode] = useState("");
  const [customCode, setCustomCode] = useState("");

  // State for profile form
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [avatar, setAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // State for password form
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // State for form submission
  const [isSaving, setIsSaving] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);

  // Refs for file inputs
  const logoInputRef = useRef<HTMLInputElement>(null);
  const avatarInputRef = useRef<HTMLInputElement>(null);

  const settingsForm = useForm<SettingsFormData>();
  const profileForm = useForm<ProfileFormData>();
  const passwordForm = useForm<PasswordFormData>();

  // Load settings when component mounts
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settings = await settingsService.getSiteSettings();
         
        setSiteSettings(settings);
        setIsLoading(false);
        setSiteName(settings.site_title || "");
        setLogoPreview(settings.logo_url || null);
        setGoogleAnalyticsCode(settings.google_analytics_code || "");
        setCustomCode(settings.custom_code || "");
        settingsForm.reset({
          logo_url: settings.logo_url || '',
          google_analytics_code: settings.google_analytics_code || '',
          custom_code: settings.custom_code || '',
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load settings",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };
    // fetchSettings();
  }, []);

  // Load user data when component mounts
  useEffect(() => {
    if (user) {
      setName(user.name || "");
      setEmail(user.email || "");
      setAvatarPreview(user.avatar || null);
    }
  }, [user]);

  // Handle logo file change
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle avatar file change
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatar(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle site settings form submission
  const handleSiteSettingsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const formData = new FormData();
      formData.append('site_title', siteName);
      if (logoFile) {
        formData.append('logo', logoFile);
      }
      formData.append('google_analytics_code', googleAnalyticsCode);
      formData.append('custom_code', customCode);

      const updatedSettings = await settingsService.updateSiteSettings(formData);
      setSiteSettings(updatedSettings);
      toast({
        title: "Success",
        description: "Site settings updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update site settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle profile form submission
  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingProfile(true);

    try {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('email', email);
      if (avatar) {
        formData.append('avatar', avatar);
      }

      await settingsService.updateProfile(formData);
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  // Handle password form submission
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords don't match",
        variant: "destructive",
      });
      return;
    }

    setIsChangingPassword(true);

    try {
      await settingsService.changePassword({
        currentPassword,
        newPassword,
        confirmPassword
      });
      
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      
      toast({
        title: "Success",
        description: "Password changed successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to change password",
        variant: "destructive",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex flex-col gap-6">
          <h1 className="text-2xl font-bold">Settings</h1>
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-admin-primary" />
            <span className="ml-2">Loading settings...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 mx-auto py-10">
        <h1 className="text-2xl font-bold">Settings</h1>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="site">Site</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5 text-admin-primary" />
                  Profile Information
                </CardTitle>
                <CardDescription>
                  Update your profile information and contact details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleProfileSubmit}>
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="flex flex-col items-center gap-4">
                      <Avatar className="h-24 w-24">
                        {avatarPreview ? (
                          <AvatarImage src={avatar ? avatarPreview : getImageUrl(avatarPreview)} alt={name} />
                        ) : (
                          <AvatarImage src={getImageUrl(user?.avatar)} alt={user?.name} />
                        )}
                        <AvatarFallback>{name?.charAt(0) || user?.name?.charAt(0) || "A"}</AvatarFallback>
                      </Avatar>
                      <input
                        type="file"
                        id="avatar"
                        ref={avatarInputRef}
                        onChange={handleAvatarChange}
                        className="hidden"
                        accept="image/*"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => avatarInputRef.current?.click()}
                      >
                        Change Picture
                      </Button>
                    </div>
                    <div className="flex-1 space-y-4">
                      <div className="grid gap-2">
                        <Label htmlFor="name">Name</Label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end mt-6">
                    <Button type="submit" className="gap-2" disabled={isUpdatingProfile}>
                      {isUpdatingProfile ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5 text-admin-primary" />
                  Security Settings
                </CardTitle>
                <CardDescription>
                  Manage your password and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handlePasswordSubmit}>
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Change Password</h3>
                    <div className="grid gap-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input
                        id="current-password"
                        type="password"
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        required
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input
                        id="new-password"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                        minLength={6}
                      />
                      <p className="text-xs text-muted-foreground">Password must be at least 6 characters long</p>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input
                        id="confirm-password"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <Separator className="my-6" />
                  <div className="flex justify-end">
                    <Button type="submit" className="gap-2" disabled={isChangingPassword}>
                      {isChangingPassword ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4" />
                          Update Security Settings
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="site">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="mr-2 h-5 w-5 text-admin-primary" />
                  Site Settings
                </CardTitle>
                <CardDescription>
                  Configure global settings for the admin panel
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleSiteSettingsSubmit}>
                  <div className="space-y-6">
                    {/* Site Title */}
                    <div className="grid gap-2">
                      <Label htmlFor="site-title">Site Title</Label>
                      <Input
                        id="site-title"
                        value={siteName}
                        onChange={(e) => setSiteName(e.target.value)}
                        placeholder="Enter site title"
                      />
                    </div>

                    {/* Logo Upload */}
                    <div className="grid gap-2">
                      <Label htmlFor="logo">Website Logo</Label>
                      <div className="flex flex-col md:flex-row gap-4 items-start">
                        {logoPreview && (
                          <div className="border rounded-md p-2 bg-slate-50 w-40 h-40 flex items-center justify-center overflow-hidden">
                            <img
                              src={logoFile ? logoPreview : getImageUrl(logoPreview)}
                              alt="Logo Preview"
                              className="max-w-full max-h-full object-contain"
                            />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex gap-2 items-center">
                            <input
                              type="file"
                              id="logo"
                              ref={logoInputRef}
                              onChange={handleLogoChange}
                              className="hidden"
                              accept="image/*"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => logoInputRef.current?.click()}
                              className="gap-2"
                            >
                              <Upload className="h-4 w-4" />
                              {logoPreview ? "Change Logo" : "Upload Logo"}
                            </Button>
                            {logoPreview && (
                              <Button
                                type="button"
                                variant="ghost"
                                onClick={() => {
                                  setLogoPreview(null);
                                  setLogoFile(null);
                                  if (logoInputRef.current) logoInputRef.current.value = "";
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                Remove
                              </Button>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mt-2">
                            Recommended size: 200x200 pixels. Max file size: 5MB.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Google Analytics Code */}
                    <div className="grid gap-2">
                      <Label htmlFor="google-analytics">Google Analytics Code</Label>
                      <Textarea
                        id="google-analytics"
                        value={googleAnalyticsCode}
                        onChange={(e) => setGoogleAnalyticsCode(e.target.value)}
                        placeholder="Paste your Google Analytics code here"
                        rows={5}
                      />
                      <p className="text-xs text-muted-foreground">
                        Paste the entire Google Analytics tracking code snippet here.
                      </p>
                    </div>

                    {/* Custom Code */}
                    <div className="grid gap-2">
                      <Label htmlFor="custom-code">Custom Code</Label>
                      <Textarea
                        id="custom-code"
                        value={customCode}
                        onChange={(e) => setCustomCode(e.target.value)}
                        placeholder="Add custom scripts, meta tags, or styles here"
                        rows={8}
                      />
                      <p className="text-xs text-muted-foreground">
                        This code will be injected before the starting &lt;body&gt; tag on all pages.
                      </p>
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" className="gap-2" disabled={isSaving}>
                        {isSaving ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4" />
                            Save Site Settings
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
