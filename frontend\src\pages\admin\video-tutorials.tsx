import { useState, useEffect } from 'react';
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogClose } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Edit, Trash, Plus, ExternalLink, Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import adminVideoService, { VideoTutorial, VideoTutorialInput } from "@/services/adminVideoService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const VideoTutorialsPage = () => {
  const { toast } = useToast();

  // State for video tutorials data
  const [videoTutorials, setVideoTutorials] = useState<VideoTutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<string[]>([]);

  // State for dialog
  const [isVideoDialogOpen, setIsVideoDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [videoFormData, setVideoFormData] = useState<VideoTutorialInput>({
    title: "",
    description: "",
    youtubeId: "",
    category: "",
    tags: [],
    duration: "",
    featured: false,
    active: true,
  });
  const [tagsInput, setTagsInput] = useState("");
  const [editingVideoId, setEditingVideoId] = useState<string | null>(null);
  const [deleteVideoId, setDeleteVideoId] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);

  // Fetch video tutorials on component mount
  useEffect(() => {
    fetchVideoTutorials();
    fetchCategories();
  }, []);

  // Fetch video tutorials from API
  const fetchVideoTutorials = async () => {
    try {
      setLoading(true);
      const response = await adminVideoService.getAllVideoTutorials();
      setVideoTutorials(response.data);
    } catch (error) {
      console.error('Error fetching video tutorials:', error);
      toast({
        title: "Error",
        description: "Failed to fetch video tutorials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await adminVideoService.getVideoCategories();
      setCategories(response);
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Don't show toast here as it's not critical
    }
  };

  // Handler for saving video (add or edit)
  const handleSaveVideo = async () => {
    if (!validateForm()) return;

    try {
      setFormSubmitting(true);
      // Prepare tags array from comma-separated input
      const tags = tagsInput.split(',').map(tag => tag.trim()).filter(Boolean);

      if (editingVideoId) {
        // Update existing video
        const videoToUpdate = videoTutorials.find(v => v._id === editingVideoId);
        if (!videoToUpdate) return;

        const videoData: Partial<VideoTutorialInput> = {
          ...videoFormData,
          tags
        };

        const response = await adminVideoService.updateVideoTutorial(editingVideoId, videoData);

        setVideoTutorials(
          videoTutorials.map(video =>
            video._id === editingVideoId ? response.data : video
          )
        );

        toast({
          title: "Success",
          description: "Video tutorial updated successfully.",
        });
      } else {
        // Create new video
        const videoData: VideoTutorialInput = {
          ...videoFormData,
          tags
        };

        const response = await adminVideoService.createVideoTutorial(videoData);
        setVideoTutorials([...videoTutorials, response.data]);

        toast({
          title: "Success",
          description: "Video tutorial added successfully.",
        });
      }

      // Reset form and close dialog
      resetForm();
      setIsVideoDialogOpen(false);
    } catch (error) {
      console.error('Error saving video tutorial:', error);
      toast({
        title: "Error",
        description: `Failed to ${editingVideoId ? 'update' : 'add'} video tutorial. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handler for deleting video
  const handleDeleteVideo = async () => {
    if (!deleteVideoId) return;

    try {
      setFormSubmitting(true);
      await adminVideoService.deleteVideoTutorial(deleteVideoId);

      setVideoTutorials(
        videoTutorials.filter(video => video._id !== deleteVideoId)
      );

      setIsDeleteDialogOpen(false);
      setDeleteVideoId(null);

      toast({
        title: "Success",
        description: "Video tutorial deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting video tutorial:', error);
      toast({
        title: "Error",
        description: "Failed to delete video tutorial. Please try again.",
        variant: "destructive",
      });
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handler for toggling active status
  const handleToggleActive = async (video: VideoTutorial) => {
    try {
      const updatedVideo = await adminVideoService.updateVideoTutorial(
        video._id,
        { active: !video.active }
      );

      setVideoTutorials(
        videoTutorials.map(v =>
          v._id === video._id ? updatedVideo.data : v
        )
      );

      toast({
        title: "Success",
        description: `Video tutorial ${updatedVideo.data.active ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error) {
      console.error('Error toggling video status:', error);
      toast({
        title: "Error",
        description: "Failed to update video status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Open video dialog for adding new video
  const openAddVideoDialog = () => {
    setEditingVideoId(null);
    resetForm();
    setIsVideoDialogOpen(true);
  };

  // Open video dialog for editing
  const openEditDialog = (video: VideoTutorial) => {
    setEditingVideoId(video._id);
    setVideoFormData({
      title: video.title,
      description: video.description,
      youtubeId: video.youtubeId,
      category: video.category,
      tags: video.tags || [],
      duration: video.duration,
      featured: video.featured,
      active: video.active,
    });
    setTagsInput(video.tags?.join(', ') || '');
    setIsVideoDialogOpen(true);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (id: string) => {
    setDeleteVideoId(id);
    setIsDeleteDialogOpen(true);
  };

  // Reset form fields
  const resetForm = () => {
    setVideoFormData({
      title: "",
      description: "",
      youtubeId: "",
      category: "",
      tags: [],
      duration: "",
      featured: false,
      active: true,
    });
    setTagsInput("");
  };

  // Validate form inputs
  const validateForm = () => {
    if (!videoFormData.title.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a title.",
        variant: "destructive",
      });
      return false;
    }

    if (!videoFormData.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a description.",
        variant: "destructive",
      });
      return false;
    }

    if (!videoFormData.youtubeId.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a YouTube ID.",
        variant: "destructive",
      });
      return false;
    }

    if (!videoFormData.category.trim()) {
      toast({
        title: "Validation Error",
        description: "Please select a category.",
        variant: "destructive",
      });
      return false;
    }

    if (!videoFormData.duration.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter the video duration.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  // Filter videos based on search query
  const filteredVideos = videoTutorials.filter(video =>
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (video.tags && video.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
  );

  return (
    <DashboardLayout>
      <div className="space-y-6 mx-auto py-10">
        <div className="flex justify-between items-center">


           <h1 className="text-3xl font-bold">Video Tutorials Management</h1>
          <Button onClick={openAddVideoDialog} className="flex items-center gap-2">
            <Plus className="h-4 w-4" /> Add New Video
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Manage Video Tutorials</h2>
          <p className="text-gray-500 mb-6">Add, edit or remove video tutorials for your users</p>

          <div className="mb-6">
            <Input
              placeholder="Search videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-md"
            />
          </div>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>YouTube ID</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">Loading...</TableCell>
                  </TableRow>
                ) : filteredVideos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center">No video tutorials found.</TableCell>
                  </TableRow>
                ) : (
                  filteredVideos.map((video) => (
                    <TableRow key={video._id}>
                      <TableCell>{video.title}</TableCell>
                      <TableCell>{video.description}</TableCell>
                      <TableCell>{video.category}</TableCell>
                      <TableCell>
                        <a
                          href={`https://youtube.com/watch?v=${video.youtubeId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:text-blue-700"
                        >
                          {video.youtubeId.substring(0, 8)}...
                        </a>
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={video.active}
                          onCheckedChange={() => handleToggleActive(video)}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => openEditDialog(video)}
                          title="Edit"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-destructive/10"
                          onClick={() => openDeleteDialog(video._id)}
                          title="Delete"
                        >
                          <Trash className="h-4 w-4 text-red-500" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
         
        </div>
      </div>

      {/* Video Dialog (Add/Edit) */}
      <Dialog open={isVideoDialogOpen} onOpenChange={setIsVideoDialogOpen}>
        <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingVideoId ? 'Edit Video Tutorial' : 'Add New Video Tutorial'}</DialogTitle>
            <DialogDescription>
              {editingVideoId
                ? 'Update video tutorial details.'
                : 'Add a new video tutorial to help users understand the platform.'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label htmlFor="title" className="text-sm font-medium">Title*</label>
              <Input
                id="title"
                placeholder="Enter video title"
                value={videoFormData.title}
                onChange={(e) => setVideoFormData({ ...videoFormData, title: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">Description*</label>
              <Textarea
                id="description"
                placeholder="Enter video description"
                value={videoFormData.description}
                onChange={(e) => setVideoFormData({ ...videoFormData, description: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">Category*</label>
              {categories.length > 0 ? (
                <Select
                  value={videoFormData.category}
                  onValueChange={(value) => setVideoFormData({ ...videoFormData, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                    <SelectItem value="other">Other (New Category)</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  id="category"
                  placeholder="e.g., Getting Started, Advanced Features"
                  value={videoFormData.category}
                  onChange={(e) => setVideoFormData({ ...videoFormData, category: e.target.value })}
                />
              )}
              {videoFormData.category === 'other' && (
                <Input
                  placeholder="Enter new category name"
                  className="mt-2"
                  onChange={(e) => setVideoFormData({ ...videoFormData, category: e.target.value })}
                />
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="youtubeId" className="text-sm font-medium">YouTube Video ID*</label>
              <Input
                id="youtubeId"
                placeholder="e.g., dQw4w9WgXcQ"
                value={videoFormData.youtubeId}
                onChange={(e) => setVideoFormData({ ...videoFormData, youtubeId: e.target.value })}
              />
              <p className="text-xs text-gray-500">
                The ID is the part after "v=" in a YouTube URL (e.g., https://www.youtube.com/watch?v=dQw4w9WgXcQ)
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="duration" className="text-sm font-medium">Duration*</label>
              <Input
                id="duration"
                placeholder="e.g., 10:30"
                value={videoFormData.duration}
                onChange={(e) => setVideoFormData({ ...videoFormData, duration: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="tags" className="text-sm font-medium">Tags (comma-separated)</label>
              <Input
                id="tags"
                placeholder="e.g., beginner, setup, tutorial"
                value={tagsInput}
                onChange={(e) => setTagsInput(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="featured"
                checked={videoFormData.featured || false}
                onCheckedChange={(checked) => setVideoFormData({ ...videoFormData, featured: checked })}
              />
              <label htmlFor="featured" className="text-sm font-medium">Featured on Dashboard</label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={videoFormData.active || true}
                onCheckedChange={(checked) => setVideoFormData({ ...videoFormData, active: checked })}
              />
              <label htmlFor="active" className="text-sm font-medium">Active</label>
            </div>
          </div>

          <div className="flex justify-end space-x-2 mt-4">
            <DialogClose asChild>
              <Button variant="outline" disabled={formSubmitting}>Cancel</Button>
            </DialogClose>
            <Button onClick={handleSaveVideo} disabled={formSubmitting}>
              {formSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {editingVideoId ? 'Updating...' : 'Saving...'}
                </>
              ) : (
                editingVideoId ? 'Update Video' : 'Save Video'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Video Tutorial</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this video tutorial? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="flex justify-end space-x-2 mt-4">
            <DialogClose asChild>
              <Button variant="outline" disabled={formSubmitting}>Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteVideo}
              disabled={formSubmitting}
            >
              {formSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Video'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default VideoTutorialsPage;