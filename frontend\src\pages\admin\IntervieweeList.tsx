import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { RefreshCw, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { aiInterviewService, InterviewSummary, PaginatedResponse } from '@/services/aiInterviewService';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import IntervieweeSummaryDialog from '@/components/ai-interview/IntervieweeSummaryDialog';
import InterviewSummaryDialog from '@/components/ai-interview/InterviewSummaryDialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Link } from 'react-router-dom';


// Extended interface for InterviewSummary with objective
interface Interviewee<PERSON>ata extends InterviewSummary {
  objective?: string;
  endDate?: string;
  createdBy?: {
    name: string;
    email: string;
    avatar?: string;
  };
  duration?: number;
  _id?: string;
}

const IntervieweeList = () => {
  const [interviewees, setInterviewees] = useState<IntervieweeData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentSummary, setCurrentSummary] = useState<IntervieweeData | null>(null);
  const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  // Fetch interviewee data with pagination
  const fetchInterviewees = async (page: number = currentPage) => {
    try {
      setIsLoading(true);
      const response = await aiInterviewService.getAdminInterviews(page, itemsPerPage);
      setInterviewees(response.data);
      setTotalPages(response.pages);
      setTotalItems(response.total);
      setCurrentPage(response.page);
    } catch (error) {
      console.error('Failed to fetch interviewee data:', error);
      toast.error('Failed to load interviewee data');
    } finally {
      setIsLoading(false);
    }
  };

  console.log('interviewees', interviewees);

  // Generate user initials for avatar fallback
  const getInitials = (name: string = '') => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2) || 'UN';
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins} mins ${secs} secs`;
  }

  // Load interviewees on page load or when page changes
  useEffect(() => {
    fetchInterviewees(currentPage);
  }, [currentPage]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Format date to readable string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // View interviewee summary
  const viewIntervieweeSummary = async (interviewee: IntervieweeData) => {
    try {
      setCurrentSummary(interviewee);
      setSummaryDialogOpen(true);
    } catch (error) {
      console.error('Failed to fetch interviewee summary:', error);
      toast.error('Failed to load interviewee summary');
    }
  };

  // Apply search filter when search query changes
  useEffect(() => {
    // Only refetch if we have a search query or if we're clearing a previous search
    if (searchQuery.trim() !== '') {
      // We're handling the search client-side for now
      // In a real app, you might want to send the search query to the backend
    }
  }, [searchQuery]);

  // Filter interviewees based on search query (client-side filtering)
  const filteredInterviewees = searchQuery.trim() === ''
    ? interviewees
    : interviewees.filter(interviewee =>
      interviewee.userName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      interviewee.company?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      interviewee.position?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      interviewee.objective?.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const renderPagination = () => {
    const pages = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(
        <Button
          key={i}
          variant={i === currentPage ? "default" : "outline"}
          size="sm"
          onClick={() => handlePageChange(i)}
          className="w-8 h-8 p-0"
        >
          {i}
        </Button>
      );
    }
    return pages;
  };

  return (
    <DashboardLayout>
      <div className="container max-w-7xl mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Interviewee List</h1>
            <p className="text-sm text-gray-500">View and manage all interview data</p>
          </div>

          <Button
            variant="outline"
            onClick={() => {
              setCurrentPage(1);
              fetchInterviewees(1);
            }}
            disabled={isLoading}
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, company, position, or objective..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                // Reset to page 1 when search query changes
                if (currentPage !== 1) {
                  setCurrentPage(1);
                }
              }}
              className="pl-10"
            />
          </div>
        </div>


        {isLoading ? (
          <div className="flex justify-center py-8">
            <RefreshCw className="w-8 h-8 animate-spin text-gray-400" />
          </div>
        ) : filteredInterviewees.length > 0 ? (

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">User</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Company Name</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Ends In</TableHead>
                  <TableHead>Objective</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInterviewees.map((interviewee) => (
                  <TableRow key={interviewee._id}>
                    <TableCell>
                      <Link to={`/admin/users/${interviewee.createdBy?._id}`} className="flex items-center gap-3 hover:underline">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={interviewee.createdBy?.avatar} alt={interviewee.createdBy?.name} />
                          <AvatarFallback>{interviewee.createdBy?.name?.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="font-medium">{interviewee.createdBy?.name || 'Unknown User'}</span>
                          <span className="text-sm text-gray-500">{interviewee.createdBy?.email || 'Unknown Email'}</span>
                        </div>
                        {/* <div className="truncate">
                                                    <span>{payment?.userName}</span>
                                                  </div> */}
                      </Link>

                      {/* <div className="flex items-center space-x-2">
                        <Avatar>
                          <AvatarImage src={interviewee.createdBy?.avatar} />
                          <AvatarFallback>{getInitials(interviewee.createdBy?.name)}</AvatarFallback>
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="font-medium">{interviewee.createdBy?.name || 'Unknown User'}</span>
                          <span className="text-sm text-gray-500">{interviewee.createdBy?.email || 'Unknown Email'}</span>
                        </div>
                      </div> */}
                    </TableCell>
                    <TableCell>{formatDate(interviewee.createdAt)}</TableCell>
                    <TableCell>{interviewee.company}</TableCell>
                    <TableCell>{interviewee.position}</TableCell>
                    <TableCell>{formatTime(interviewee.duration)}</TableCell>
                    <TableCell>{interviewee.objective?.substring(0, 50) || "N/A"}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewIntervieweeSummary(interviewee)}
                      >
                        Summary
                      </Button>
                    </TableCell>



                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {searchQuery ? 'No results found for your search' : 'No interviewee data available'}
            </p>
          </div>
        )}



        {/* Pagination */}

        {totalPages > 1 && (
          <div className="flex justify-center mt-4 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex gap-1">{renderPagination()}</div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Interviewee Summary Dialog */}
        <IntervieweeSummaryDialog
          open={summaryDialogOpen}
          onOpenChange={setSummaryDialogOpen}
          summary={currentSummary}
        />
      </div>
    </DashboardLayout>
  );
};

export default IntervieweeList;