import express from 'express';
import multer from 'multer';
import path from 'path';
import {
    createAIInterview,
    getAllAIInterviews,
    getAIInterview,
    deleteAIInterview,
    getAIModels,
    updateInterviewSession,
    getAIInterviewBySessionId,
    getAllAIInterviewSessionsByUserId
} from '../controllers/aiInterviewController.js';
import { protectSharedRoutes } from '../middleware/authMiddleware.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/resumes/');
    },
    filename: function (req, file, cb) {
        const filename = `resume-${Date.now()}${path.extname(file.originalname)}`;
        cb(null, filename);
    }
});

// File filter for resume uploads
const fileFilter = (req, file, cb) => {
    // Allow pdf, doc, docx files
    if (
        file.mimetype === 'application/pdf' ||
        file.mimetype === 'application/msword' ||
        file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only PDF, DOC and DOCX files are allowed.'), false);
    }
};

const upload = multer({
    storage,
    fileFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Handle path normalization for files after upload
const handleFilePath = (req, res, next) => {
    if (req.file) {
        // Convert Windows backslashes to forward slashes
        req.file.path = req.file.path.replace(/\\/g, '/');
    }
    next();
};

// Routes
router.post('/', protectSharedRoutes, upload.single('resume'), handleFilePath, createAIInterview);
router.get('/', protectSharedRoutes, getAllAIInterviews);
router.get('/models', protectSharedRoutes, getAIModels);
// router.get('/models', getAIModels);
router.get('/session/:sessionId', protectSharedRoutes, getAIInterviewBySessionId);
router.get('/:id', protectSharedRoutes, getAIInterview);
router.delete('/:id', protectSharedRoutes, deleteAIInterview);
router.put('/:id', protectSharedRoutes, updateInterviewSession);
router.get('/user/:userId', protectSharedRoutes, getAllAIInterviewSessionsByUserId);

// Also add a route for the speech endpoint
// This is a placeholder if you need to implement the actual speech endpoint
router.post('/speech', protectSharedRoutes, (req, res) => {
    const { transcript, sessionId } = req.body;
    // This would be implemented with actual processing logic
    res.status(200).json({
        success: true,
        data: {
            answer: `AI response to: "${transcript}"`
        }
    });
});

export default router; 