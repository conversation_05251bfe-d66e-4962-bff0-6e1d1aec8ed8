import { api } from '../lib/axios';
import axios from 'axios';

export interface SubscriptionPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billingInterval: 'daily' | 'weekly' | 'monthly' | 'yearly';
    intervalCount: number;
    isActive: boolean;
    features: string[];
    subscriptionCredits: number;
    productId: string;
    priceId: string;
    planId: string;
    createdAt: string;
    updatedAt: string;

}

export interface UserSubscription {
    status: string;
    planName: string;
    planId: string;
    startDate: string;
    endDate: string;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    stripeSubscriptionId: string;
    autoRenew: boolean;
    cancelAtPeriodEnd: boolean;
    price: number;
    currency: string;
    subscriptionCredits: number;
    billingInterval: string;
    availableCredits?: number;
    creditsPurchased?: number;
}

export interface SubscriptionUpgradeDetails {
    currentPlan: {
        id: string;
        name: string;
        price: number;
        credits: number;
    };
    targetPlan: {
        id: string;
        name: string;
        price: number;
        credits: number;
    };
    upgradeAmount: number;
    currency: string;
}

export interface SubscriptionHistory {
    id: string;
    eventType: string;
    status: string;
    planName: string;
    startDate: string;
    endDate: string;
    amount: number;
    currency: string;
    stripeSubscriptionId: string;
    createdAt: string;
    upgradeDetails?: {
        originalAmount: number;
        balanceAmount: number;
        previousCredits: number;
        newCredits: number;
    };
}

export interface PaymentHistory {
    id: string;
    amount: number;
    currency: string;
    status: string;
    planName: string;
    invoiceId: string;
    transactionDate: string;
    createdAt: string;
    subscriptionCredits: number;
    creditsPurchased: number;
    metadata?: {
        type?: string;
        upgradeFrom?: string;
        upgradeTo?: string;
    };
}

export interface DashboardSummary {
    subscription: {
        status: string;
        planName: string;
        startDate: string;
        endDate: string;
        price: number;
        currency: string;
        renewsOn: string;
        autoRenew: boolean;
        credits: number;
    } | null;
    latestPayment: {
        amount: number;
        currency: string;
        date: string;
        planName: string;
        nextPaymentDate: string | null;
    } | null;
    recentActivity: Array<{
        id: string;
        type: string;
        description: string;
        amount: number;
        currency: string;
        status: string;
        date: string;
    }>;
    credits: number;
}

const subscriptionService = {
    // Get all subscription plans
    getPlans: async (): Promise<SubscriptionPlan[]> => {
        try {
            const response = await api.get('/subscriptions/available-plans');
            return response.data.plans || [];
        } catch (error) {
            console.error('Error fetching subscription plans:', error);
            return [];
        }
    },

    // Get current user's subscription
    getCurrentSubscription: async () => {
        try {
            const response = await api.get('/subscriptions/current');

            return response.data;
        } catch (error) {
            console.error('Error fetching current subscription:', error);
            return { hasActiveSubscription: false };
        }
    },

    // Get subscription history
    getSubscriptionHistory: async () => {
        try {
            const response = await api.get('/subscriptions/history');
            return response.data.history || [];
        } catch (error) {
            console.error('Error fetching subscription history:', error);
            return [];
        }
    },

    // Get user subscription payment history
    getUserSubscriptionPaymentHistory: async (userId: string, page: number = 1, limit: number = 10) => {
        try {
            const response = await api.get(`/subscriptions/user-subscription-payment-history/${userId}`, {
                params: { page, limit }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching user subscription payment history:', error);
            return [];
        }
    },
    // Get payment history with pagination
    getPaymentHistory: async (page: number = 1, limit: number = 10): Promise<{
        payments: PaymentHistory[];
        page: number;
        limit: number;
        total: number;
        pages: number;
    }> => {
        try {
            const response = await api.get('/subscriptions/user-payment-history', {
                params: { page, limit }
            });

            return {
                payments: response.data.payments || [],
                page: response.data.page || 1,
                limit: response.data.limit || 10,
                total: response.data.total || 0,
                pages: response.data.pages || 1
            };
        } catch (error) {
            console.error('Error fetching payment history:', error);
            return {
                payments: [],
                page: 1,
                limit: 10,
                total: 0,
                pages: 1
            };
        }
    },

    // Get dashboard summary
    getDashboardSummary: async () => {
        try {
            const response = await api.get('/subscriptions/dashboard-summary');
            return response.data.dashboardSummary;
        } catch (error) {
            console.error('Error fetching dashboard summary:', error);
            throw error;
        }
    },

    // Create a checkout session
    createCheckoutSession: async (priceId: object) => {

        try {
            const response = await api.post('/stripe/create-checkout-session', priceId);
            return response.data;
        } catch (error) {
            console.error('Error creating checkout session:', error);

            // Provide more specific error message
            if (axios.isAxiosError(error) && error.response) {
                throw new Error(error.response.data?.message || 'Failed to create checkout session');
            }

            throw new Error('Failed to connect to payment service');
        }
    },

    // Calculate upgrade amount
    calculateUpgradeAmount: async (planId: string): Promise<SubscriptionUpgradeDetails> => {
        try {
            const response = await api.get(`/subscriptions/calculate-upgrade/${planId}`);
            return response.data;
        } catch (error) {
            console.error('Error calculating upgrade amount:', error);

            if (axios.isAxiosError(error) && error.response) {
                throw new Error(error.response.data?.message || 'Failed to calculate upgrade amount');
            }

            throw new Error('Failed to connect to subscription service');
        }
    },

    // Handle subscription upgrade
    upgradeSubscription: async (planId: string) => {
        try {
            const response = await api.post(`/subscriptions/upgrade/${planId}`);
            return response.data;
        } catch (error) {
            console.error('Error upgrading subscription:', error);

            if (axios.isAxiosError(error) && error.response) {
                throw new Error(error.response.data?.message || 'Failed to upgrade subscription');
            }

            throw new Error('Failed to connect to subscription service');
        }
    },

    // Cancel subscription
    cancelSubscription: async (cancelImmediately = false) => {
        try {
            const response = await api.post('/subscriptions/cancel', { cancelImmediately });
            return response.data;
        } catch (error) {
            console.error('Error cancelling subscription:', error);

            if (axios.isAxiosError(error) && error.response) {
                throw new Error(error.response.data?.message || 'Failed to cancel subscription');
            }

            throw new Error('Failed to connect to subscription service');
        }
    }
};

export default subscriptionService;