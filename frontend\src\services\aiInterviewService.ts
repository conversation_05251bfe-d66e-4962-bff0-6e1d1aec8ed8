import api from './api';
import { ApiResponse } from './apiTypes';

// Types
export interface AIInterview {
    _id: string;
    scenario: string;
    company: string;
    position: string;
    objective?: string;
    resumeFile?: string;
    createdAt: string;
    updatedAt: string;
    createdBy: {
        _id: string;
        name: string;
        email: string;
    };
    scenarioDescription?: string;
}

export interface ConversationEntry {
    question: string;
    answer: string;
    timestamp: string;
}

export interface InterviewSessionMetadata {
    duration: number;
    endTime: string;
}

export interface InterviewSummary {
    _id: string;
    company: string;
    position: string;
    userName: string;
    userEmail: string;
    duration: number;
    endsIn: string;
    createdAt: string;
    conversations: ConversationEntry[];
    createdBy?: {
        _id: string;
        name: string;
        email: string;
        avatar?: string;
    };
}

export interface AIModel {
    is_enabled: boolean;
    apikey: string;
    model: string;
}

export interface AIModelsConfig {
    openai: AIModel;
    gemini: AIModel;
}

export interface PaginatedResponse<T> {
    success: boolean;
    count: number;
    total: number;
    page: number;
    pages: number;
    data: T[];
}

// AI Interview API endpoints
export const aiInterviewService = {
    // Get all AI interview configurations
    getAllInterviews: async (): Promise<ApiResponse<AIInterview[]>> => {
        try {
            const response = await api.get('/interview/session');
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to fetch interviews';
            throw new Error(errorMessage);
        }
    },

    // Get a single AI interview configuration
    getInterview: async (id: string): Promise<ApiResponse<AIInterview>> => {
        try {
            const response = await api.get(`/interview/session/${id}`);
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to fetch interview';
            throw new Error(errorMessage);
        }
    },

    // Create a new AI interview configuration
    createInterview: async (formData: FormData): Promise<ApiResponse<AIInterview>> => {
        try {
            // Custom config for multipart/form-data
            const config = {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            };

            const response = await api.post('/interview/session', formData, config);
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create interview';
            throw new Error(errorMessage);
        }
    },

    // Delete an AI interview configuration
    deleteInterview: async (id: string): Promise<ApiResponse<{}>> => {
        try {
            const response = await api.delete(`/interview/session/${id}`);
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to delete interview';
            throw new Error(errorMessage);
        }
    },

    // Start a new interview session
    startInterviewSession: async (id: string): Promise<ApiResponse<{ sessionId: string }>> => {
        try {
            const response = await api.post(`/interview/session/start`, { interviewId: id });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to start interview session';
            throw new Error(errorMessage);
        }
    },

    // Get interview session details
    getInterviewSession: async (sessionId: string): Promise<ApiResponse<AIInterview>> => {
        try {
            const response = await api.get(`/interview/session/${sessionId}`);
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get interview session';
            throw new Error(errorMessage);
        }
    },

    // Get AI response to a transcript
    getAIResponse: async (transcript: string, sessionId: string): Promise<ApiResponse<{ answer: string }>> => {
        try {
            const response = await api.post(`/interview/speech`, {
                transcript,
                sessionId
            });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get AI response';
            throw new Error(errorMessage);
        }
    },

    // Submit interview results
    submitInterviewResult: async (
        sessionId: string,
        conversations: ConversationEntry[],
        metadata: InterviewSessionMetadata
    ): Promise<ApiResponse<{}>> => {
        try {

            const response = await api.put(`/interview/session/${sessionId}`, {
                sessionId,
                conversations,
                duration: metadata.duration,
                endDate: metadata.endTime
            });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to submit interview results';
            throw new Error(errorMessage);
        }
    },

    // Get interview summary
    getInterviewSummary: async (sessionId: string): Promise<ApiResponse<InterviewSummary>> => {
        try {
            const response = await api.get(`/interview/summary/${sessionId}`);
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get interview summary';
            throw new Error(errorMessage);
        }
    },

    // Get user's interview list with pagination
    getUserInterviews: async (userId: string, page: number = 1, limit: number = 10): Promise<PaginatedResponse<InterviewSummary>> => {
        try {
            const response = await api.get(`/interview/session/user/${userId}`, {
                params: { page, limit }
            });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get user interviews';
            throw new Error(errorMessage);
        }
    },

    // Get admin interview list with pagination
    getAdminInterviews: async (page: number = 1, limit: number = 10): Promise<PaginatedResponse<InterviewSummary>> => {
        try {
            const response = await api.get(`/interview/session`, {
                params: { page, limit }
            });
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get admin interviews';
            throw new Error(errorMessage);
        }
    },

    // Get AI models configuration
    getAIModels: async (): Promise<ApiResponse<AIModelsConfig>> => {
        try {
            const response = await api.get('/interview/session/models');
            return response.data;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to fetch AI models';
            throw new Error(errorMessage);
        }
    }
};

export default aiInterviewService;