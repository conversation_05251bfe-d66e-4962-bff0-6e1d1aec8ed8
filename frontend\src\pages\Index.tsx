import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useUser } from "@clerk/clerk-react";
import { Button } from "@/components/ui/button";

const Index = () => {
  const navigate = useNavigate();
   const { isAuthenticated, isLoading, user } = useAuth();
  const { isSignedIn, isLoaded } = useUser();

  useEffect(() => {
    // Check authentication states after both Clerk and Auth Context are loaded
    if (!isLoading && !isLoaded) return;

    if (isAuthenticated && isLoaded && user?.role === 'admin') {
      navigate('/admin/dashboard');
    } else if (isSignedIn && user?.role === 'user') {
      navigate('/user/dashboard');
    }
  }, [isAuthenticated, isLoading, isSignedIn, isLoaded, navigate, user]);

  // Show loading state while checking authentication
  if (isLoading || !isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-admin-primary"></div>
      </div>
    );
  }

  return (
    <>
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Welcome</h1>
          <p className="text-gray-600">Please select your destination</p>
        </div>
        <div className="space-y-4">
          <Button 
            variant="default" 
            className="w-full bg-admin-primary hover:bg-admin-primary/90"
            onClick={() => navigate('/admin/login')}
          >
            Admin Panel
          </Button>
          <Button 
            variant="outline" 
            className="w-full border-purple-600 text-purple-600 hover:bg-purple-50"
            onClick={() => navigate('/user/login')}
          >
            User Login
          </Button>
        </div>
      </div>
        
      </div>
      </>
  );
};

export default Index;
